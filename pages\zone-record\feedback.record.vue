<template>
  <div class="feedback-record-page all flex f-column">
    <!-- 顶部搜索和头部区域 -->
    <div class="header-section">
      <div class="search-container">
        <div class="search-wrapper">
          <wd-search v-model="params.name" hide-cancel placeholder="搜索反馈小区名称..." custom-class="modern-search" @change="debounceHandlesearchChange" @search="handleSearch" />
        </div>
      </div>

      <!-- 紧凑型日期范围筛选 -->
      <div class="date-filter-compact">
        <div class="date-range-wrapper">
          <div class="date-range-input">
            <wd-icon name="calendar" size="16px" color="#667eea"></wd-icon>
            <wd-calendar type="daterange" custom-class=" f-1" v-model="showDatePicker" allow-same-day :formatter="formatter" @confirm="handleConfirm">
              <span class="date-range-text">{{ getDateRangeText() }}</span>
            </wd-calendar>
            <wd-icon name="arrow-down" size="14px" color="#999"></wd-icon>
          </div>
          <div class="quick-filters">
            <div class="quick-filter-item" @click="setQuickFilter('lastMonth')">上月</div>
            <div class="quick-filter-item" @click="setQuickFilter('lastWeek')">上周</div>
            <div class="quick-filter-item" @click="setQuickFilter('yesterday')">昨天</div>
            <div class="quick-filter-item" @click="setQuickFilter('today')">今天</div>
            <div class="quick-filter-item" @click="setQuickFilter('week')">本周</div>
            <div class="quick-filter-item" @click="setQuickFilter('month')">本月</div>
            <div class="quick-filter-item" @click="setQuickFilter('clear')" v-if="params.start || params.end">清除</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 列表内容 -->
    <div class="f-1 overflow-auto pad-16 back-f5f5f5">
      <!-- 空状态 -->
      <div v-if="pendingList.length === 0" class="empty-state f-xy-center f-column">
        <div class="empty-icon f-xy-center mar-B24">
          <wd-icon :name="params.name ? 'search' : 'chat'" size="48px" color="#c5d2d9"></wd-icon>
        </div>
        <div class="empty-text fon-S28 color-999">
          {{ params.name ? '未找到相关反馈' : '暂无待处理反馈' }}
        </div>
        <div class="empty-desc fon-S24 color-ccc mar-T8">
          {{ params.name ? '请尝试调整搜索条件' : '所有反馈都已处理完成' }}
        </div>
      </div>

      <!-- 反馈卡片列表 -->
      <template v-for="(item, index) in pendingList" :key="item.FeedbackID">
        <div @click="handleItemClick(item)" class="feedback-card" :class="{ 'card-unread': !item.IsReadByCurrentUser }" :style="{ animationDelay: index * 0.1 + 's' }">
          <!-- 未读角标 -->
          <div v-if="item.IsReadByCurrentUser === 0 && item.newestReply" class="unread-indicator">
            <div class="indicator-pulse"></div>
          </div>

          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="header-left f-y-center f-1">
              <div class="feedback-icon f-xy-center">
                <wd-icon name="chat" size="24px" color="#667eea"></wd-icon>
              </div>
              <div class="header-content f-1">
                <div class="feedback-title fon-S32 fon-W600 color-333">{{ item.FeedbackContent }}</div>
                <div class="feedback-time fon-S24 color-999 mar-T4">{{ formatTime(item?.newestReply?.ModifyTime) }}</div>
              </div>
            </div>
            <div class="header-right">
              <div class="priority-badge" :class="item.pendingCount > 0 ? 'priority-high' : 'priority-normal'">
                <wd-icon :name="item.pendingCount > 0 ? 'warning' : 'check-circle'" size="16px"></wd-icon>
              </div>
            </div>
          </div>

          <!-- 统计信息区域 -->
          <div class="stats-section">
            <div class="stats-grid">
              <div class="stat-item pending">
                <div class="stat-number fon-S36 fon-W700">{{ item.pendingCount || 0 }}</div>
                <div class="stat-label fon-S22">待处理</div>
              </div>
              <div class="stat-divider"></div>
              <div class="stat-item completed">
                <div class="stat-number fon-S36 fon-W700">{{ item.processedCount || 0 }}</div>
                <div class="stat-label fon-S22">已完成</div>
              </div>
              <div class="stat-divider"></div>
              <div class="stat-item total">
                <div class="stat-number fon-S36 fon-W700">{{ (item.pendingCount || 0) + (item.processedCount || 0) }}</div>
                <div class="stat-label fon-S22">总计</div>
              </div>
            </div>
          </div>

          <!-- 最新回复内容 -->
          <div class="reply-section" v-if="item?.newestReply?.ReplyContent">
            <div class="reply-header fon-S24 color-666 mar-B12">最新回复</div>
            <div class="reply-content">
              <div class="reply-text fon-S28 color-333">{{ item?.newestReply?.ReplyContent }}</div>
            </div>
          </div>

          <!-- 底部操作区域 -->
          <div class="card-footer">
            <div class="footer-left f-y-center">
              <div class="file-info f-y-center" @click.stop="handleToDetail(item)">
                <wd-icon name="folder" size="16px" color="#40a9ff"></wd-icon>
                <span class="file-code fon-S24 color-666 mar-L8">{{ item.FileCode || '无档案编码' }}</span>
              </div>
            </div>
            <div class="footer-right f-y-center">
              <span class="action-text fon-S24 color-999 mar-R8">查看详情</span>
              <div class="action-arrow f-xy-center">
                <wd-icon name="arrow-right" size="16px" color="#c5d2d9"></wd-icon>
              </div>
            </div>
          </div>

          <!-- 点击波纹效果 -->
          <div class="ripple-effect"></div>
        </div>
      </template>
    </div>
    <wd-pagination v-model="params.page" :total="total" :pageSize="params.pageSize" @change="handlePaginationChange" show-icon />

    <FeedbackPopup ref="feedbackRef" @close="onClosePopup"></FeedbackPopup>
  </div>
  <wd-message-box />
</template>

<script setup>
import { reactive, ref, watch } from 'vue'
import { onLoad, onBackPress } from '@dcloudio/uni-app'
import debounce from 'lodash/debounce'
import { useToast, useMessage } from 'wot-design-uni'

import { feedbackPendingListApi } from '../../services/model/feedback.issue'
import FeedbackPopup from '@/components/FeedbackPopup/index.vue'

const feedbackRef = ref(null)
const pendingList = ref([])
const total = ref(0)
const message = useMessage()

const params = reactive({ name: '', page: 1, pageSize: 10, start: '', end: '' })

// 日期选择器相关状态
const showDatePicker = ref(false)

// 日期格式化函数
const formatter = (day) => {
  const date = new Date(day.date)
  const now = new Date()
  const year = date.getFullYear()
  const month = date.getMonth()
  const da = date.getDate()
  const nowYear = now.getFullYear()
  const nowMonth = now.getMonth()
  const nowDa = now.getDate()

  if (year === nowYear && month === nowMonth && da === nowDa) {
    day.topInfo = '今天'
  }

  if (day.type === 'start') {
    day.bottomInfo = '开始'
  }

  if (day.type === 'end') {
    day.bottomInfo = '结束'
  }

  if (day.type === 'same') {
    day.bottomInfo = '开始/结束'
  }

  return day
}
onLoad(() => getFeedbackPendingList({ page: params.page }))

onBackPress(() => {
  const isShow = feedbackRef.value.getFeedbackIsOpen()
  if (isShow) {
    feedbackRef.value.close()
    return true
  }
})

function onClosePopup() {
  getFeedbackPendingList()
}

// 页码改变
function handlePaginationChange({ value }) {
  getFeedbackPendingList({ page: value })
}

// 获取反馈列表
async function getFeedbackPendingList(obj) {
  const newParams = { ...params, ...obj }

  // 格式化时间参数为 YYYY-MM-DD 格式
  if (newParams.start) {
    newParams.start = formatDateForApi(newParams.start)
  }
  if (newParams.end) {
    newParams.end = formatDateForApi(newParams.end)
  }

  try {
    const { data, pagination } = await feedbackPendingListApi(newParams)
    pendingList.value =
      data.map((item) => {
        item.newestReply = item.newestReply ? JSON.parse(item.newestReply) : null
        item.createUserInfo = item.createUserInfo ? JSON.parse(item.createUserInfo) : {}
        return item
      }) || []

    total.value = pagination.total
  } catch (error) {
    console.error('获取反馈列表失败:', error)
    pendingList.value = []
    total.value = 0
  }
}

// 格式化时间
function formatTime(timeStr) {
  if (!timeStr) return '--'
  try {
    const date = new Date(timeStr)
    const now = new Date()
    const diff = now - date
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days === 0) {
      return '今天'
    } else if (days === 1) {
      return '昨天'
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }
  } catch (error) {
    return timeStr.slice(0, 10) || '--'
  }
}

// 处理卡片点击
function handleItemClick(item) {
  uni.vibrateShort({ type: 'medium' })
  feedbackRef.value.getFeedbackDetail(item.FeedbackID)
}

function handleToDetail({ FileCode }) {
  uni.navigateTo({ url: `/pages/zone-record/detail?xqbm=${FileCode}` })
}

// 搜索功能
const debounceHandlesearchChange = debounce(handleSearch, 500)

function handleSearch({ value }) {
  params.page = 1
  getFeedbackPendingList({ name: value, page: 1 })
}

function getDateRangeText() {
  if (params.start && params.end) {
    return `${formatDateForApi(params.start)} 至 ${formatDateForApi(params.end)}`
  } else if (params.start) {
    return `从 ${formatDateForApi(params.start)}`
  } else if (params.end) {
    return `至 ${formatDateForApi(params.end)}`
  }
  return '选择日期范围'
}

function setQuickFilter(type) {
  uni.vibrateShort({ type: 'medium' })

  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth()
  const date = today.getDate()

  switch (type) {
    case 'today':
      params.start = formatDateForApi(today)
      params.end = formatDateForApi(today)
      break
    case 'yesterday':
      const yesterday = new Date(today)
      yesterday.setDate(date - 1)
      params.start = formatDateForApi(yesterday)
      params.end = formatDateForApi(yesterday)
      break
    case 'week':
      const dayOfWeek = today.getDay()
      // 计算距离周一的天数（周日为0，需要特殊处理）
      const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1

      const weekStart = new Date(today)
      weekStart.setDate(date - daysFromMonday)
      const weekEnd = new Date(today)
      weekEnd.setDate(date - daysFromMonday + 6)
      params.start = formatDateForApi(weekStart)
      params.end = formatDateForApi(weekEnd)
      break
    case 'lastWeek':
      const lastWeekDayOfWeek = today.getDay()
      const lastWeekDaysFromMonday = lastWeekDayOfWeek === 0 ? 6 : lastWeekDayOfWeek - 1

      const lastWeekStart = new Date(today)
      lastWeekStart.setDate(date - lastWeekDaysFromMonday - 7)
      const lastWeekEnd = new Date(today)
      lastWeekEnd.setDate(date - lastWeekDaysFromMonday - 1)
      params.start = formatDateForApi(lastWeekStart)
      params.end = formatDateForApi(lastWeekEnd)
      break
    case 'month':
      const monthStart = new Date(year, month, 1)
      const monthEnd = new Date(year, month + 1, 0)
      params.start = formatDateForApi(monthStart)
      params.end = formatDateForApi(monthEnd)
      break
    case 'lastMonth':
      const lastMonthStart = new Date(year, month - 1, 1)
      const lastMonthEnd = new Date(year, month, 0)
      params.start = formatDateForApi(lastMonthStart)
      params.end = formatDateForApi(lastMonthEnd)
      break
    case 'clear':
      params.start = ''
      params.end = ''
      break
  }

  // 应用筛选
  params.page = 1
  getFeedbackPendingList({ page: 1 })

  if (type !== 'clear') {
    uni.showToast({
      title: '筛选已应用',
      icon: 'success'
    })
  } else {
    uni.showToast({
      title: '筛选已清除',
      icon: 'success'
    })
  }
}

function handleConfirm({ value }) {
  // value 是一个包含开始和结束日期的数组或单个日期
  if (Array.isArray(value) && value.length >= 2) {
    // 日期范围选择
    params.start = formatDateForApi(value[0])
    params.end = formatDateForApi(value[1])
  } else if (Array.isArray(value) && value.length === 1) {
    // 单日期选择
    params.start = formatDateForApi(value[0])
    params.end = formatDateForApi(value[0])
  } else if (value) {
    // 单个日期值
    params.start = formatDateForApi(value)
    params.end = formatDateForApi(value)
  } else {
    params.start = ''
    params.end = ''
  }

  // 重置页码并应用筛选
  params.page = 1
  showDatePicker.value = false
  getFeedbackPendingList({ page: 1 })

  uni.showToast({
    title: '筛选已应用',
    icon: 'success'
  })
}

// 格式化日期为 YYYY-MM-DD 格式
function formatDateForApi(dateStr) {
  if (!dateStr) return ''

  try {
    const date = new Date(dateStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (error) {
    console.error('日期格式化失败:', error)
    return ''
  }
}

// 获取副标题文本
function getSubtitleText() {
  if (params.name) {
    return '搜索结果'
  }

  if (params.start || params.end) {
    if (params.start && params.end) {
      return `时间范围：${formatDateForApi(params.start)} 至 ${formatDateForApi(params.end)}`
    } else if (params.start) {
      return `开始时间：${formatDateForApi(params.start)}`
    } else if (params.end) {
      return `结束时间：${formatDateForApi(params.end)}`
    }
  }

  return '待处理反馈列表'
}
</script>

<style lang="less" scoped>
.feedback-record-page {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100%;
}

/* 顶部搜索和头部区域 */
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 20rpx 24rpx 20rpx;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
}

.search-container {
  margin-bottom: 16rpx;
}

.search-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 0 16rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 紧凑型日期筛选样式 */
.date-filter-compact {
  margin-bottom: 16rpx;
}

.date-range-wrapper {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.date-range-input {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 12rpx;
  /deep/.wd-calendar {
    z-index: 1 !important;
  }
}

.date-range-text {
  flex: 1;
  font-size: 26rpx;
  color: #2d3748;
  font-weight: 500;
}

.quick-filters {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.quick-filter-item {
  padding: 8rpx 16rpx;
  background: rgba(102, 126, 234, 0.08);
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #667eea;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  font-weight: 500;

  &:active {
    background: rgba(102, 126, 234, 0.15);
    transform: scale(0.95);
  }
}

.header-content {
  position: relative;
  z-index: 2;
}

.title-text {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.subtitle-text {
  color: rgba(255, 255, 255, 0.8);
}

.stats-badge {
  width: 56rpx;
  height: 56rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  border: 1rpx solid rgba(255, 255, 255, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: -2rpx;
    left: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 30rpx;
    z-index: -1;
    opacity: 0.5;
    animation: pulse 2s infinite;
  }
}

/* 空状态样式 */
.empty-state {
  padding: 120rpx 40rpx;

  .empty-icon {
    width: 120rpx;
    height: 120rpx;
    background: linear-gradient(135deg, #f0f0f0 0%, #e8e8e8 100%);
    border-radius: 60rpx;
  }
}

/* 反馈卡片样式 */
.feedback-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  animation: slideInUp 0.6s ease-out both;
  border: 1rpx solid rgba(102, 126, 234, 0.08);
  margin-bottom: 24rpx;
  cursor: pointer;

  &:active {
    transform: translateY(-6rpx) scale(0.98);
    box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.15);
  }

  // 未读状态
  &.card-unread {
    border: 2rpx solid rgba(102, 126, 234, 0.2);
    box-shadow: 0 6rpx 32rpx rgba(102, 126, 234, 0.12);

    .unread-indicator {
      opacity: 1;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4rpx;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      z-index: 1;
    }
  }
}

/* 未读指示器 */
.unread-indicator {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 10;
  opacity: 0;

  .indicator-pulse {
    width: 16rpx;
    height: 16rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    animation: pulseGlow 2s infinite;
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 28rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.header-left {
  gap: 20rpx;
}

.feedback-icon {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
  border-radius: 16rpx;
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.feedback-title {
  line-height: 1.4;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.priority-badge {
  width: 40rpx;
  height: 40rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &.priority-high {
    background: linear-gradient(135deg, rgba(255, 71, 87, 0.1) 0%, rgba(255, 107, 122, 0.05) 100%);
    color: #ff4757;
    border: 1rpx solid rgba(255, 71, 87, 0.2);
  }

  &.priority-normal {
    background: linear-gradient(135deg, rgba(46, 213, 115, 0.1) 0%, rgba(123, 237, 159, 0.05) 100%);
    color: #2ed573;
    border: 1rpx solid rgba(46, 213, 115, 0.2);
  }
}

/* 统计信息区域 */
.stats-section {
  padding: 20rpx 28rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.01) 100%);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.stats-grid {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;

  &.pending .stat-number {
    color: #ff6b7a;
  }

  &.completed .stat-number {
    color: #2ed573;
  }

  &.total .stat-number {
    color: #667eea;
  }
}

.stat-number {
  font-weight: 700;
  line-height: 1.2;
}

.stat-label {
  color: #8892b0;
  margin-top: 4rpx;
  font-weight: 500;
}

.stat-divider {
  width: 1rpx;
  height: 40rpx;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
  margin: 0 20rpx;
}

/* 最新回复区域 */
.reply-section {
  padding: 20rpx 28rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.reply-header {
  font-weight: 600;
  opacity: 0.8;
}

.reply-content {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.02);
  margin-top: 12rpx;
}

.reply-text {
  line-height: 1.6;
  word-break: break-word;
  color: #5a6c7d;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 28rpx 28rpx;
}

.file-info {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background: rgba(64, 169, 255, 0.05);
  border: 1rpx solid rgba(64, 169, 255, 0.1);

  &:active {
    background: rgba(64, 169, 255, 0.1);
    transform: scale(0.95);
  }
}

.file-code {
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-arrow {
  width: 36rpx;
  height: 36rpx;
  background: rgba(197, 210, 217, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

/* 点击波纹效果 */
.ripple-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: 24rpx;
}

.feedback-card:active .ripple-effect {
  opacity: 1;
}

.feedback-card:active .action-arrow {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(4rpx);
}

/* 响应式设计优化 */
@media (max-width: 750rpx) {
  .feedback-card {
    margin-bottom: 20rpx;
    border-radius: 20rpx;
  }

  .card-header {
    padding: 24rpx 24rpx 16rpx;
  }

  .feedback-icon {
    width: 48rpx;
    height: 48rpx;
  }

  .stats-section {
    padding: 16rpx 24rpx;
  }

  .stat-number {
    font-size: 32rpx;
  }

  .reply-section {
    padding: 16rpx 24rpx;
  }

  .card-footer {
    padding: 16rpx 24rpx 24rpx;
  }

  .priority-badge {
    width: 36rpx;
    height: 36rpx;
  }

  .action-arrow {
    width: 32rpx;
    height: 32rpx;
  }

  /* 紧凑型日期筛选响应式 */
  .date-range-wrapper {
    padding: 12rpx;
  }

  .date-range-input {
    padding: 10rpx 12rpx;
    margin-bottom: 10rpx;
  }

  .date-range-text {
    font-size: 24rpx;
  }

  .quick-filters {
    gap: 6rpx;
  }

  .quick-filter-item {
    padding: 6rpx 12rpx;
    font-size: 20rpx;
  }
}

/* 动画效果 */
@keyframes pulseGlow {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 12rpx rgba(102, 126, 234, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.5;
  }
}

/* 自定义搜索框样式 */
:deep(.modern-search) {
  .wd-search__input {
    padding-left: 80rpx !important;
    font-size: 28rpx !important;
    color: #2d3748 !important;

    &::placeholder {
      color: #a0aec0 !important;
    }
  }

  .wd-search__block {
    background: transparent !important;
    border: none !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .feedback-record-page {
    background: linear-gradient(180deg, #1a1a1a 0%, #2a2a2a 100%);
  }

  .header-section {
    background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
  }

  .feedback-card {
    background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
  }

  .title-text {
    color: rgba(255, 255, 255, 0.95) !important;
  }

  .subtitle-text {
    color: rgba(255, 255, 255, 0.8) !important;
  }

  .feedback-content {
    background: rgba(64, 169, 255, 0.05);
    border-left-color: rgba(64, 169, 255, 0.3);
  }
}
</style>
