<template>
  <div class="facility-card">
    <!-- 卡片头部 -->
    <div class="card-header" @click="toggleMainCollapse">
      <div class="header-left">
        <div class="facility-icon">
          <image class="icon-img" :src="`/static/img/${facilityData.key}.svg`"></image>
        </div>
        <div class="facility-info">
          <h3 class="facility-title">{{ facilityData.key }}</h3>
          <p class="facility-subtitle">设施统计数据</p>
        </div>
      </div>
      <div class="header-right">
        <div class="total-badge">
          <span class="total-number">{{ formatTotal(facilityData.total) }}</span>
          <span class="total-unit">{{ facilityData.unit == 'km' ? 'km' : '个' }}</span>
        </div>
        <div class="collapse-btn">
          <wd-icon :name="isMainCollapsed ? 'chevron-down' : 'chevron-up'" size="24rpx" color="#49a2de" />
        </div>
      </div>
    </div>

    <!-- 卡片内容 - 可折叠区域 -->
    <div class="card-content-wrapper" :class="{ 'wrapper-collapsed': isMainCollapsed }">
      <div class="card-content">
        <!-- 统计概览 -->
        <div class="stats-overview">
          <div class="stats-container" @click="emit('toTrigger', { key: facilityData.key, type: 'all' })">
            <!-- 中央圆形图表 -->
            <div class="central-chart">
              <div class="chart-background">
                <div class="chart-ring municipal-ring" :style="{ '--percentage': getMunicipalPercentage() + '%' }"></div>
                <div class="chart-ring plot-ring" :style="{ '--percentage': getPlotPercentage() + '%' }"></div>
              </div>
              <div class="chart-center">
                <div class="total-value">
                  <span class="total-number">{{ formatTotal(facilityData.total, 1) }}</span>
                  <span class="total-unit">{{ facilityData.unit == 'km' ? 'km' : '个' }}</span>
                </div>
                <div class="total-label">总计</div>
              </div>
            </div>

            <!-- 左侧市政数据 -->
            <div class="stat-panel municipal-panel">
              <div class="panel-decoration">
                <div class="decoration-line"></div>
                <div class="decoration-dot"></div>
              </div>
              <div class="panel-content">
                <div class="panel-icon">
                  <div class="icon-container">
                    <span class="icon-emoji">🏛️</span>
                    <div class="icon-pulse"></div>
                  </div>
                </div>
                <div class="panel-info">
                  <div class="info-label">市政设施</div>
                  <div class="info-subtitle">Municipal</div>
                  <div class="info-metrics">
                    <span class="metric-value">{{ formatValue(facilityData.municipal) }}</span>
                    <span class="metric-unit">{{ facilityData.unit == 'km' ? 'km' : '个' }}</span>
                  </div>
                  <div class="info-percentage">{{ getMunicipalPercentage() }}% 占比</div>
                </div>
              </div>
            </div>

            <!-- 右侧小区数据 -->
            <div class="stat-panel plot-panel">
              <div class="panel-decoration">
                <div class="decoration-line"></div>
                <div class="decoration-dot"></div>
              </div>
              <div class="panel-content">
                <div class="panel-icon">
                  <div class="icon-container">
                    <span class="icon-emoji">🏘️</span>
                    <div class="icon-pulse"></div>
                  </div>
                </div>
                <div class="panel-info">
                  <div class="info-label">小区设施</div>
                  <div class="info-subtitle">Residential</div>
                  <div class="info-metrics">
                    <span class="metric-value">{{ formatValue(facilityData.plot) }}</span>
                    <span class="metric-unit">{{ facilityData.unit == 'km' ? 'km' : '个' }}</span>
                  </div>
                  <div class="info-percentage">{{ getPlotPercentage() }}% 占比</div>
                </div>
              </div>
            </div>

            <!-- 连接线 -->
            <div class="connection-lines">
              <div class="line municipal-line"></div>
              <div class="line plot-line"></div>
            </div>
          </div>
        </div>

        <!-- 水务所数据 -->
        <FacilityDistribution
          @trigger="(val) => emit('toTrigger', { ...val, ukey: facilityData.key, type: 'waterdept' })"
          :data="facilityData.waterdept"
          title="水务所分布"
          subtitle="按水务所分类的设施统计"
          icon="🏢"
          :unit="facilityData.unit"
          :default-collapsed="true"
          @toggle="onWaterdeptToggle"
        />

        <!-- 网格数据 -->
        <FacilityDistribution
          @trigger="(val) => emit('toTrigger', { ...val, ukey: facilityData.key, type: 'grid' })"
          :data="facilityData.grid"
          title="网格分布"
          subtitle="按网格区域的设施统计"
          icon="🗂️"
          :unit="facilityData.unit"
          :default-collapsed="true"
          @toggle="onGridToggle"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FacilityDistribution from './FacilityDistribution.vue'

const props = defineProps({
  facilityData: {
    type: Object,
    required: true
  },
  defaultCollapsed: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['mainToggle', 'waterdeptToggle', 'gridToggle', 'toTrigger'])

const isMainCollapsed = ref(props.defaultCollapsed)

const toggleMainCollapse = () => {
  isMainCollapsed.value = !isMainCollapsed.value
  emit('mainToggle', isMainCollapsed.value)
}

const onWaterdeptToggle = (collapsed) => {
  emit('waterdeptToggle', collapsed)
}

const onGridToggle = (collapsed) => {
  emit('gridToggle', collapsed)
}

const formatValue = (value) => {
  if (props.facilityData.unit === 'km') {
    return value.toFixed(2)
  }
  return Math.round(value)
}

const formatTotal = (value, decimals = 2) => {
  if (props.facilityData.unit === 'km') {
    return value.toFixed(decimals)
  }
  return Math.round(value)
}

const getMunicipalPercentage = () => {
  const total = props.facilityData.plot + props.facilityData.municipal
  if (total === 0) return 0
  return Math.round((props.facilityData.municipal / total) * 100)
}

const getPlotPercentage = () => {
  const total = props.facilityData.plot + props.facilityData.municipal
  if (total === 0) return 0
  return Math.round((props.facilityData.plot / total) * 100)
}
</script>

<style lang="less" scoped>
// 设施卡片样式
.facility-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  border-radius: 20rpx;
  margin-top: 36rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #e8f4fd;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6rpx;
    background: linear-gradient(135deg, #49a2de 0%, #667eea 100%);
  }

  // 卡片头部
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 32rpx;
    background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
    border-bottom: 1rpx solid #e8f4fd;
    position: relative;
    overflow: hidden;
    cursor: pointer;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      animation: headerShine 3s infinite;
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .facility-icon {
        width: 64rpx;
        height: 64rpx;
        // background: linear-gradient(135deg, #49a2de 0%, #667eea 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        // box-shadow: 0 6rpx 16rpx rgba(73, 162, 222, 0.4);
        position: relative;
        transition: all 0.3s ease;

        &::before {
          content: '';
          position: absolute;
          inset: -2rpx;
          border-radius: 50%;
          background: linear-gradient(135deg, #49a2de, #667eea, #764ba2);
          z-index: -1;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: scale(1.05) rotate(5deg);
          box-shadow: 0 8rpx 20rpx rgba(73, 162, 222, 0.5);

          &::before {
            opacity: 1;
          }
        }

        .icon-img {
          // width: 32rpx;
          // height: 32rpx;
          // filter: brightness(0) invert(1);
          // transition: transform 0.3s ease;
        }

        &:hover .icon-img {
          transform: scale(1.1);
        }
      }

      .facility-info {
        .facility-title {
          font-size: 36rpx;
          font-weight: 700;
          color: #2c3e50;
          margin: 0 0 6rpx 0;
          line-height: 1.2;
          background: linear-gradient(135deg, #2c3e50, #49a2de);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .facility-subtitle {
          font-size: 24rpx;
          color: #7f8c8d;
          margin: 0;
          line-height: 1.2;
          position: relative;

          &::before {
            content: '●';
            color: #49a2de;
            margin-right: 8rpx;
            font-size: 12rpx;
            animation: pulse 2s infinite;
          }
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .total-badge {
        background: linear-gradient(135deg, rgba(73, 162, 222, 0.08) 0%, rgba(102, 126, 234, 0.08) 100%);
        border: 1rpx solid rgba(73, 162, 222, 0.15);
        border-radius: 20rpx;
        padding: 12rpx 18rpx;
        display: flex;
        align-items: center;
        gap: 8rpx;
        box-shadow: 0 2rpx 8rpx rgba(73, 162, 222, 0.1);
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10rpx);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 1rpx;
          background: linear-gradient(90deg, transparent, rgba(73, 162, 222, 0.3), transparent);
          animation: badgeShimmer 3s infinite;
        }

        &:hover {
          transform: translateY(-1rpx);
          background: linear-gradient(135deg, rgba(73, 162, 222, 0.12) 0%, rgba(102, 126, 234, 0.12) 100%);
          border-color: rgba(73, 162, 222, 0.25);
          box-shadow: 0 4rpx 16rpx rgba(73, 162, 222, 0.15);
        }

        .total-number {
          font-size: 28rpx;
          font-weight: 700;
          color: #49a2de;
          font-family: 'Courier New', monospace;
          line-height: 1;
          text-shadow: 0 1rpx 2rpx rgba(73, 162, 222, 0.1);
        }

        .total-unit {
          font-size: 20rpx;
          font-weight: 500;
          color: #667eea;
          background: rgba(73, 162, 222, 0.1);
          padding: 4rpx 8rpx;
          border-radius: 10rpx;
          line-height: 1;
          border: 1rpx solid rgba(73, 162, 222, 0.1);
        }
      }

      // 折叠按钮样式
      .collapse-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56rpx;
        height: 56rpx;
        background: linear-gradient(135deg, rgba(73, 162, 222, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
        border: 2rpx solid rgba(73, 162, 222, 0.2);
        border-radius: 50%;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10rpx);
        cursor: pointer;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          inset: 0;
          background: linear-gradient(135deg, rgba(73, 162, 222, 0.2), rgba(102, 126, 234, 0.2));
          border-radius: 50%;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          background: linear-gradient(135deg, rgba(73, 162, 222, 0.15) 0%, rgba(102, 126, 234, 0.15) 100%);
          border-color: rgba(73, 162, 222, 0.3);
          transform: scale(1.05);
          box-shadow: 0 4rpx 12rpx rgba(73, 162, 222, 0.2);

          &::before {
            opacity: 1;
          }
        }
      }
    }
  }

  // 卡片内容包装器
  .card-content-wrapper {
    max-height: 2000rpx;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &.wrapper-collapsed {
      max-height: 0;
    }

    .card-content {
      padding: 16rpx;
    }
  }
}

@keyframes headerShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes badgeShimmer {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

// 统计概览样式
.stats-overview {
  margin-bottom: 32rpx;

  .stats-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 32rpx;
    background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
    border-radius: 20rpx;
    border: 1rpx solid #e8f4fd;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
    overflow: hidden;
    min-height: 280rpx;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3rpx;
      background: linear-gradient(90deg, #49a2de, #667eea, #764ba2, #f093fb);
      animation: gradientShift 3s ease-in-out infinite;
    }

    // 中央圆形图表
    .central-chart {
      position: relative;
      width: 160rpx;
      height: 160rpx;
      z-index: 3;

      .chart-background {
        position: absolute;
        inset: 0;
        border-radius: 50%;

        .chart-ring {
          position: absolute;
          inset: 0;
          border-radius: 50%;
          transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);

          &.municipal-ring {
            background: conic-gradient(from -90deg, #1976d2 0deg, #1976d2 var(--percentage, 0%), transparent var(--percentage, 0%));
            mask: radial-gradient(circle at center, transparent 50rpx, black 50rpx, black 68rpx, transparent 68rpx);
          }

          &.plot-ring {
            background: conic-gradient(from calc(-90deg + var(--municipal-angle, 0deg)), #c2185b 0deg, #c2185b var(--percentage, 0%), transparent var(--percentage, 0%));
            mask: radial-gradient(circle at center, transparent 50rpx, black 50rpx, black 68rpx, transparent 68rpx);
          }
        }
      }

      .chart-center {
        position: absolute;
        inset: 20rpx;
        border-radius: 50%;
        background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        border: 2rpx solid rgba(255, 255, 255, 0.8);

        .total-value {
          display: flex;
          align-items: baseline;
          gap: 4rpx;

          .total-number {
            font-size: 32rpx;
            font-weight: 700;
            color: #2c3e50;
            font-family: 'Courier New', monospace;
            line-height: 1;
          }

          .total-unit {
            font-size: 18rpx;
            color: #7f8c8d;
            font-weight: 500;
          }
        }

        .total-label {
          font-size: 16rpx;
          color: #95a5a6;
          margin-top: 4rpx;
          font-weight: 500;
        }
      }
    }

    // 左右侧面板
    .stat-panel {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 200rpx;
      z-index: 2;

      &.municipal-panel {
        left: 0;
      }

      &.plot-panel {
        right: 0;
      }

      .panel-decoration {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;

        .decoration-line {
          width: 80rpx;
          height: 2rpx;
          background: linear-gradient(90deg, transparent, currentColor, transparent);
          opacity: 0.3;
        }

        .decoration-dot {
          position: absolute;
          right: -4rpx;
          top: 50%;
          transform: translateY(-50%);
          width: 8rpx;
          height: 8rpx;
          border-radius: 50%;
          background: currentColor;
          opacity: 0.6;
        }
      }

      .panel-content {
        position: relative;
        z-index: 2;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
        backdrop-filter: blur(10rpx);
        border-radius: 16rpx;
        padding: 20rpx;
        border: 1rpx solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2rpx);
          box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
        }

        .panel-icon {
          display: flex;
          justify-content: center;
          margin-bottom: 12rpx;

          .icon-container {
            position: relative;
            width: 48rpx;
            height: 48rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
            border: 2rpx solid rgba(255, 255, 255, 0.5);
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

            .icon-emoji {
              font-size: 24rpx;
              filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
            }

            .icon-pulse {
              position: absolute;
              inset: -4rpx;
              border-radius: 50%;
              background: linear-gradient(135deg, currentColor, transparent);
              opacity: 0.2;
              animation: pulse 2s infinite;
            }
          }
        }

        .panel-info {
          text-align: center;

          .info-label {
            font-size: 22rpx;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2rpx;
            line-height: 1.2;
          }

          .info-subtitle {
            font-size: 16rpx;
            color: #7f8c8d;
            margin-bottom: 12rpx;
            font-weight: 400;
            opacity: 0.8;
          }

          .info-metrics {
            display: flex;
            align-items: baseline;
            justify-content: center;
            gap: 4rpx;
            margin-bottom: 8rpx;

            .metric-value {
              font-size: 28rpx;
              font-weight: 700;
              font-family: 'Courier New', monospace;
              line-height: 1;
            }

            .metric-unit {
              font-size: 18rpx;
              color: #7f8c8d;
              font-weight: 500;
            }
          }

          .info-percentage {
            font-size: 18rpx;
            font-weight: 500;
            opacity: 0.8;
          }
        }
      }

      &.municipal-panel {
        color: #1976d2;

        .panel-decoration {
          left: 120rpx;

          .decoration-line {
            background: linear-gradient(90deg, transparent, #1976d2, transparent);
          }

          .decoration-dot {
            background: #1976d2;
          }
        }

        .panel-content {
          .info-metrics .metric-value {
            color: #1976d2;
          }

          .info-percentage {
            color: #1976d2;
          }
        }
      }

      &.plot-panel {
        color: #c2185b;

        .panel-decoration {
          right: 120rpx;

          .decoration-line {
            background: linear-gradient(90deg, transparent, #c2185b, transparent);
          }

          .decoration-dot {
            background: #c2185b;
            left: -4rpx;
            right: auto;
          }
        }

        .panel-content {
          .info-metrics .metric-value {
            color: #c2185b;
          }

          .info-percentage {
            color: #c2185b;
          }
        }
      }
    }

    // 连接线
    .connection-lines {
      position: absolute;
      inset: 0;
      z-index: 1;

      .line {
        position: absolute;
        top: 50%;
        height: 2rpx;
        background: linear-gradient(90deg, transparent, currentColor, transparent);
        opacity: 0.2;
        animation: lineFlow 3s infinite;

        &.municipal-line {
          left: 200rpx;
          right: 50%;
          color: #1976d2;
          animation-delay: 0s;
        }

        &.plot-line {
          left: 50%;
          right: 200rpx;
          color: #c2185b;
          animation-delay: 1.5s;
        }
      }
    }
  }
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes lineFlow {
  0%,
  100% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.4;
  }
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}
</style>
