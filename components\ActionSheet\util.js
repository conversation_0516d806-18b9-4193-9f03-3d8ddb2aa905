export function addUnit(num) {
  return Number.isNaN(Number(num)) ? `${num}` : `${num}px`
}

export function objToStyle(styles) {
  if (typeof styles === 'object') {
    return Object.keys(styles)
      .filter((key) => styles[key] != null && styles[key] !== '')
      .map((key) => [kebabCase(key), styles[key]].join(':'))
      .join(';')
  }
  return ''
}

import { ref } from 'vue'

// 导出一个函数，用于处理触摸事件
export function useTouch() {
  // 定义一个响应式变量，用于存储触摸方向
  const direction = ref('')
  // 定义一个响应式变量，用于存储水平位移
  const deltaX = ref(0)
  // 定义一个响应式变量，用于存储垂直位移
  const deltaY = ref(0)
  // 定义一个响应式变量，用于存储水平偏移量
  const offsetX = ref(0)
  // 定义一个响应式变量，用于存储垂直偏移量
  const offsetY = ref(0)
  // 定义一个响应式变量，用于存储触摸点初始水平位置
  const startX = ref(0)
  // 定义一个响应式变量，用于存储触摸点初始垂直位置
  const startY = ref(0)

  // 监听触摸开始事件
  function touchStart(event) {
    // 获取触摸点信息
    const touch = event.touches[0]
    // 重置方向
    direction.value = ''
    // 重置水平位移
    deltaX.value = 0
    // 重置垂直位移
    deltaY.value = 0
    // 重置水平偏移量
    offsetX.value = 0
    // 重置垂直偏移量
    offsetY.value = 0
    // 记录触摸点初始水平位置
    startX.value = touch.clientX
    // 记录触摸点初始垂直位置
    startY.value = touch.clientY
  }

  // 监听触摸移动事件
  function touchMove(event) {
    const touch = event.touches[0]
    // 计算水平位移
    deltaX.value = touch.clientX - startX.value
    // 计算垂直位移
    deltaY.value = touch.clientY - startY.value
    // 计算水平偏移量
    offsetX.value = Math.abs(deltaX.value)
    // 计算垂直偏移量
    offsetY.value = Math.abs(deltaY.value)
    // 判断触摸方向
    direction.value = offsetX.value > offsetY.value ? 'horizontal' : offsetX.value < offsetY.value ? 'vertical' : ''
  }

  // 返回一个对象，包含触摸事件处理函数和响应式变量
  return {
    touchStart,
    touchMove,
    direction,
    deltaX,
    deltaY,
    offsetX,
    offsetY,
    startX,
    startY
  }
}
