<template>
  <div class="detail-table-container">
    <!-- 集成的标题组件 -->
    <div class="section-title">
      <div class="title-container" @click="toggleCollapse">
        <!-- 左侧装饰线 -->
        <div class="title-decoration"></div>

        <!-- 图标 -->
        <div class="title-icon-wrapper">
          <image :src="iconSrc" class="title-icon" mode="aspectFit" />
        </div>

        <!-- 标题文字 -->
        <div class="title-text">
          <div class="main-title">{{ title }}</div>
          <div class="sub-title" v-if="subtitle">{{ subtitle }}</div>
        </div>

        <!-- 右侧折叠按钮 -->
        <div class="title-actions">
          <div class="collapse-btn">
            <wd-icon :name="isCollapsed ? 'chevron-down' : 'chevron-up'" size="24rpx" :color="color" />
          </div>
        </div>
      </div>

      <!-- 底部分割线 -->
      <div class="title-divider"></div>
    </div>

    <!-- 可折叠的表格内容 -->
    <div class="table-content" :class="{ collapsed: isCollapsed }">
      <div class="detail-table">
        <!-- 表头 -->
        <div class="flex table-header">
          <div class="header-cell">{{ tableType }}</div>
          <div class="header-cell">类型</div>
          <div class="header-cell">
            <span class="header-text">市政</span>
            <span class="header-unit">km</span>
          </div>
          <div class="header-cell">
            <span class="header-text">小区</span>
            <span class="header-unit">km</span>
          </div>
          <div class="header-cell">
            <span class="header-text">合计</span>
            <span class="header-unit">km</span>
          </div>
        </div>

        <!-- 数据行 -->
        <template v-for="(item, index) in props.data" :key="index">
          <div class="flex fon-S20 data-row" @click="handleClick(item)">
            <!-- 第一列：部门/网格名称 -->
            <div class="name-cell">{{ item.key }}</div>

            <!-- 数据区域 -->
            <div class="data-section">
              <template v-for="(type, typeIndex) in item.types" :key="typeIndex">
                <div class="flex type-row">
                  <div class="type-cell">{{ type.name }}</div>
                  <div class="value-cell">{{ formatNumber(type.municipal) }}</div>
                  <div class="value-cell">{{ formatNumber(type.plot) }}</div>
                  <div class="value-cell total-cell">{{ formatNumber(type.total) }}</div>
                </div>
              </template>

              <!-- 每行的排水管渠合计 -->
              <div class="flex type-row row-summary">
                <div class="type-cell row-summary-type">排水管渠</div>
                <div class="value-cell row-summary-value">{{ formatNumber(getRowMunicipalTotal(item)) }}</div>
                <div class="value-cell row-summary-value">{{ formatNumber(getRowPlotTotal(item)) }}</div>
                <div class="value-cell total-cell row-summary-total">{{ formatNumber(getRowTotal(item)) }}</div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

/**
 * DetailTable 组件属性定义
 * 提供可折叠的详细数据表格展示功能
 */
const props = defineProps({
  // ==================== 数据相关 ====================

  /**
   * 表格数据源
   * @type {Array} 包含部门/网格数据的数组
   * @example [{ key: '部门A', types: [{ name: '类型1', municipal: 10, plot: 5, total: 15 }] }]
   */
  data: {
    type: Array,
    default: () => []
  },

  /**
   * 表格类型标识
   * @type {String} 用于区分不同类型的表格
   */
  tableType: {
    type: String,
    default: ''
  },

  // ==================== 标题相关 ====================

  /**
   * 主标题
   * @type {String} 表格的主要标题，必填
   */
  title: {
    type: String,
    required: true
  },

  /**
   * 副标题
   * @type {String} 表格的辅助说明文字，可选
   */
  subtitle: {
    type: String,
    default: ''
  },

  /**
   * 图标路径
   * @type {String} 标题区域显示的图标文件路径
   */
  iconSrc: {
    type: String,
    default: '/static/img/home/<USER>'
  },

  // ==================== 样式主题 ====================

  /**
   * 主题色
   * @type {String} 组件的主题颜色，支持十六进制颜色值
   */
  color: {
    type: String,
    default: '#49a2de'
  },

  // ==================== 交互状态 ====================

  /**
   * 默认折叠状态
   * @type {Boolean} 组件初始化时是否为折叠状态
   */
  defaultCollapsed: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['toggleCollapse'])

// 折叠状态
const isCollapsed = ref(props.defaultCollapsed)

// 切换折叠状态
const toggleCollapse = () => {
  uni.vibrateShort({ type: 'medium' })
  isCollapsed.value = !isCollapsed.value
}

// 格式化数字显示
const formatNumber = (value) => {
  if (typeof value !== 'number') return '0.00'
  return value.toFixed(2)
}

// 计算每行市政合计
const getRowMunicipalTotal = (item) => {
  if (!item.types || item.types.length === 0) return 0
  return item.types.reduce((total, type) => total + (type.municipal || 0), 0)
}

// 计算每行小区合计
const getRowPlotTotal = (item) => {
  if (!item.types || item.types.length === 0) return 0
  return item.types.reduce((total, type) => total + (type.plot || 0), 0)
}

// 计算每行总合计
const getRowTotal = (item) => {
  if (!item.types || item.types.length === 0) return 0
  return item.types.reduce((total, type) => total + (type.total || 0), 0)
}

function handleClick(item) {
  emit('toggleCollapse', item)
}
</script>

<style lang="less" scoped>
.detail-table-container {
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.18);
}

// 集成的标题样式
.section-title {
  margin-bottom: 16rpx;

  .title-container {
    display: flex;
    align-items: center;
    padding: 16rpx 20rpx;
    background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
    border-radius: 12rpx;
    border: 1rpx solid #e8f4fd;
    box-shadow: 0 2rpx 8rpx rgba(73, 162, 222, 0.08);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 4rpx 16rpx rgba(73, 162, 222, 0.15);
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4rpx;
      background: v-bind(color);
      border-radius: 0 2rpx 2rpx 0;
    }
  }

  .title-decoration {
    width: 6rpx;
    height: 32rpx;
    background: v-bind(color);
    border-radius: 3rpx;
    margin-right: 16rpx;
    box-shadow: 0 2rpx 4rpx rgba(73, 162, 222, 0.3);
  }

  .title-icon-wrapper {
    width: 48rpx;
    height: 48rpx;
    background: v-bind(color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;
    box-shadow: 0 4rpx 8rpx rgba(73, 162, 222, 0.2);

    .title-icon {
      width: 28rpx;
      height: 28rpx;
      filter: brightness(0) invert(1);
    }
  }

  .title-text {
    flex: 1;

    .main-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #2c3e50;
      line-height: 1.2;
      margin-bottom: 4rpx;
    }

    .sub-title {
      font-size: 24rpx;
      color: #7f8c8d;
      line-height: 1.2;
    }
  }

  .title-actions {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-left: 16rpx;
  }

  .collapse-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56rpx;
    height: 56rpx;
    background: linear-gradient(135deg, rgba(73, 162, 222, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
    border: 2rpx solid rgba(73, 162, 222, 0.2);
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10rpx);
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg, rgba(73, 162, 222, 0.2), rgba(102, 126, 234, 0.2));
      border-radius: 50%;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(73, 162, 222, 0.15) 0%, rgba(102, 126, 234, 0.15) 100%);
      border-color: rgba(73, 162, 222, 0.3);
      transform: scale(1.05);
      box-shadow: 0 4rpx 12rpx rgba(73, 162, 222, 0.2);

      &::before {
        opacity: 1;
      }
    }
  }

  .title-divider {
    height: 2rpx;
    background: linear-gradient(90deg, transparent 0%, v-bind(color) 20%, v-bind(color) 80%, transparent 100%);
    // margin-top: 12rpx;
    opacity: 0.3;
  }
}

// 可折叠的表格内容
.table-content {
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: none; // 移除最大高度限制，让表格内容完全展开
  opacity: 1;

  &.collapsed {
    max-height: 0;
    opacity: 0;
    margin-top: -16rpx;
    overflow: hidden; // 确保折叠时内容被隐藏
  }
}

.detail-table {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #e8f4fd;

  .table-header {
    background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
    font-weight: 600;
    padding: 16rpx 0;
    border-bottom: 2rpx solid #d6ebf5;

    .header-cell {
      flex: 1;
      text-align: center;
      padding: 8rpx;
      font-size: 28rpx;
      color: #2c3e50;
      display: flex;
      align-items: baseline;
      justify-content: center;
      gap: 4rpx;

      .header-text {
        font-size: 28rpx;
        font-weight: 600;
      }

      .header-unit {
        font-size: 20rpx;
        font-weight: 500;
        opacity: 0.8;
        color: #5a6c7d;
      }
    }
  }

  .data-row {
    border-bottom: 1rpx solid #eea341;
    min-height: 80rpx;
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    .name-cell {
      width: 140rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f8fbff 0%, #f0f8ff 100%);
      border-right: 1rpx solid #e8f4fd;
      font-weight: 600;
      font-size: 24rpx;
      color: #34495e;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 20%;
        bottom: 20%;
        width: 2rpx;
        background: linear-gradient(180deg, transparent 0%, #3498db 50%, transparent 100%);
      }
    }

    .data-section {
      flex: 1;
      background-color: white;

      .type-row {
        border-bottom: 1rpx solid #f8fbff;
        transition: background-color 0.2s ease;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: #fafcff;
        }

        .type-cell {
          width: 120rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #f0f8ff 0%, #e8f4fd 100%);
          border-right: 1rpx solid #e8f4fd;
          font-size: 24rpx;
          padding: 12rpx 8rpx;
          color: #5a6c7d;
          font-weight: 500;
        }

        .value-cell {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 12rpx 8rpx;
          font-size: 26rpx;
          border-right: 1rpx solid #f0f8ff;
          color: #2c3e50;
          font-weight: 500;

          &:last-child {
            border-right: none;
          }
        }

        .total-cell {
          background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
          font-weight: 600;
          color: #27ae60;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 20%;
            bottom: 20%;
            width: 3rpx;
            background: #27ae60;
            border-radius: 0 2rpx 2rpx 0;
          }
        }
      }

      // 每行合计样式
      .row-summary {
        border: 2rpx solid #f88745;
        background: linear-gradient(135deg, #f0f8ff 0%, #e8f4fd 100%);

        &:hover {
          background: linear-gradient(135deg, #e8f4fd 0%, #d6ebf5 100%);
        }

        .row-summary-type {
          width: 120rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, v-bind(color) 0%, rgba(73, 162, 222, 0.8) 100%);
          color: white;
          font-size: 22rpx;
          padding: 10rpx 8rpx;
          font-weight: 600;
          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
          border-right: 1rpx solid #e8f4fd;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 20%;
            bottom: 20%;
            width: 2rpx;
            background: white;
            border-radius: 0 1rpx 1rpx 0;
            opacity: 0.8;
          }
        }

        .row-summary-value {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 10rpx 8rpx;
          font-size: 24rpx;
          border-right: 1rpx solid #e8f4fd;
          color: #2c3e50;
          font-weight: 600;
          background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);

          &:last-child {
            border-right: none;
          }
        }

        .row-summary-total {
          background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
          color: white;
          font-weight: 700;
          font-size: 26rpx;
          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
          position: relative;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 20%;
            bottom: 20%;
            width: 3rpx;
            background: white;
            border-radius: 0 2rpx 2rpx 0;
            opacity: 0.8;
          }
        }
      }
    }

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);

      .name-cell {
        background: linear-gradient(135deg, #e8f4fd 0%, #d6ebf5 100%);
      }
    }

    &:nth-child(even) {
      .data-section {
        background-color: #fafcff;
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .section-title {
    .title-container {
      padding: 12rpx 16rpx;
    }

    .title-text .main-title {
      font-size: 28rpx;
    }

    .title-text .sub-title {
      font-size: 20rpx;
    }

    .title-icon-wrapper {
      width: 40rpx;
      height: 40rpx;

      .title-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }

    .title-actions {
      gap: 12rpx;
      margin-left: 12rpx;
    }

    .collapse-btn {
      width: 48rpx;
      height: 48rpx;
    }
  }

  .detail-table {
    .table-header .header-cell {
      font-size: 24rpx;
      padding: 6rpx;
    }

    .data-row {
      .name-cell {
        width: 100rpx;
        font-size: 22rpx;
      }

      .data-section .type-row {
        .type-cell {
          width: 100rpx;
          font-size: 20rpx;
        }

        .value-cell {
          font-size: 22rpx;
          padding: 10rpx 6rpx;
        }
      }
    }

    // 行合计响应式
    .data-row .data-section .row-summary {
      .row-summary-type {
        width: 100rpx;
        font-size: 20rpx;
        padding: 8rpx 6rpx;
      }

      .row-summary-value {
        font-size: 20rpx;
        padding: 8rpx 6rpx;
      }

      .row-summary-total {
        font-size: 22rpx;

        &::after {
          font-size: 14rpx;
          right: 4rpx;
        }
      }
    }
  }
}
</style>
