import { cache } from '@/utils/cache.js'
class WXReauest {
  #basConfig
  constructor(config) {
    this.#basConfig = config
  }
  request(config) {
    return new Promise((resolve, reject) => {
      const token = cache.get('token')
      const header = {}
      header.Authorization = token ? 'Bearer ' + token : null

      wx.request({
        ...this.#basConfig,
        ...config,
        header,
        url: this.#basConfig.baseUrl + config.url,
        success: (res) => {
          // 登录接口报错抛出异常
          if (config.url == '/api/api/User/login' && res.statusCode === 401) return reject(res)
          // 判断登录是否过期
          if (res.statusCode === 401) {
            uni.navigateTo({ url: '/pages/login/index' })
            reject(res)
          } else if (res.statusCode === 200) {
            if (res.data.code === 401) uni.navigateTo({ url: '/pages/login/index' })
            resolve(res.data)
          } else {
            reject(res)
          }
        },
        fail: (err) => reject(err)
      })
    })
  }

  get(url, config) {
    if (config?.params) {
      const query = Object.keys(config.params)
        .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(config.params[key])}`)
        .join('&')
      url += (url.includes('?') ? '&' : '?') + query
    }
    return this.request({ url, method: 'GET', ...config })
  }
  post(url, data, config) {
    return this.request({ url, method: 'POST', data, ...config })
  }
  put(url, data, config) {
    return this.request({ url, method: 'PUT', data, ...config })
  }
  delete(url, config) {
    if (config?.params) {
      const query = Object.keys(config.params)
        .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(config.params[key])}`)
        .join('&')
      url += (url.includes('?') ? '&' : '?') + query
    }
    return this.request({ url, method: 'DELETE', ...config })
  }
}

export default WXReauest
