<template>
  <div class="action-sheet" :style="rootStyle" @touchstart.passive="handleTouchStart" @touchmove.passive="handleTouchMove" @touchend="handleTouchEnd" @touchcancel="handleTouchEnd">
    <div class="header"></div>
    <scroll-view @touchmove="handleScrollTouchMove" class="content" :scroll-y="scrollY">
      <slot></slot>
    </scroll-view>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { addUnit, useTouch } from './util'

const emit = defineEmits(['change', 'draggingEnd'])
const props = defineProps({
  duration: { type: Number, default: 600 },
  anchors: { type: Array, default: () => [0, 100] },
  isDragging: { type: Boolean, default: true },
  dragAmplitude: { type: Number, default: 100 }
})

const touch = useTouch()
let startY = 0 // 起始位置
const dragging = ref(false) // 拖拽状态
const windowHeight = uni.getSystemInfoSync().windowHeight
const proportion = defineModel({ default: 30 })
const lastProportion = proportion.value < 0 ? 0 : proportion.value > 100 ? 100 : proportion.value
const height = ref((lastProportion / 100) * windowHeight) // 动态高度
const scrollY = computed(() => proportion.value === props.anchors[props.anchors.length - 1] || !props.isDragging)

// 计算边界值，借鉴wd-floating-panel的做法
const boundary = computed(() => ({
  min: props.anchors[0] ? (props.anchors[0] / 100) * windowHeight : 0,
  max: props.anchors[props.anchors.length - 1] ? (props.anchors[props.anchors.length - 1] / 100) * windowHeight : Math.round(windowHeight * 0.6)
}))

// 计算样式，借鉴wd-floating-panel的做法，使用固定最大高度作为容器高度
const rootStyle = computed(() => {
  return {
    height: addUnit(boundary.value.max),
    transform: `translateY(calc(100% + ${addUnit(-height.value)}))`,
    transition: !dragging.value ? `transform ${props.duration}ms cubic-bezier(0.18, 0.89, 0.32, 1.28)` : 'none'
  }
})

const anchorHeight = computed(() => {
  return props.anchors.map((item) => (item / 100) * windowHeight)
})

watch(proportion, (newVal) => {
  updateHeight((newVal / 100) * windowHeight)
})

// 触屏事件
function handleTouchStart(event) {
  if (!props.isDragging) return

  // 检查是否点击的是 textarea 或 input 元素，如果是则不处理拖拽
  const target = event.target || event.currentTarget
  if (target && (target.tagName === 'TEXTAREA' || target.tagName === 'INPUT' || target.classList.contains('uni-textarea-textarea') || target.classList.contains('uni-input-input'))) {
    return
  }

  touch.touchStart(event)
  dragging.value = true
  startY = -height.value
}
// 移动事件
function handleTouchMove(event) {
  if (!props.isDragging) return

  // 检查是否在 textarea 或 input 元素上移动，如果是则不处理拖拽
  const target = event.target || event.currentTarget
  if (target && (target.tagName === 'TEXTAREA' || target.tagName === 'INPUT' || target.classList.contains('uni-textarea-textarea') || target.classList.contains('uni-input-input'))) {
    return
  }

  touch.touchMove(event)
  const moveY = touch.deltaY.value + startY
  const min = anchorHeight.value[0]
  const max = anchorHeight.value[anchorHeight.value.length - 1]
  if (-moveY < min || -moveY > max) return
  updateHeight(-moveY)
}

// scroll-view 的触摸移动事件处理
function handleScrollTouchMove(event) {
  // 检查是否在 textarea 或 input 元素上，如果是则不阻止事件
  const target = event.target || event.currentTarget
  if (target && (target.tagName === 'TEXTAREA' || target.tagName === 'INPUT' || target.classList.contains('uni-textarea-textarea') || target.classList.contains('uni-input-input'))) {
    return // 不阻止 textarea/input 的默认行为
  }

  // 对于其他元素，调用原来的处理逻辑
  event.stopPropagation()
  event.preventDefault()
  handleTouchMove(event)
}

// 结束事件
function handleTouchEnd() {
  const per = {
    deltaY: -touch.deltaY.value,
    deltaX: -touch.deltaX.value,
    offsetX: touch.offsetX.value,
    offsetY: touch.offsetY.value,
    startX: touch.startX.value,
    startY: touch.startY.value
  }

  emit('draggingEnd', per)

  if (!props.isDragging) return
  dragging.value = false
  if (Math.abs(touch.deltaY.value) < props.dragAmplitude) {
    return updateHeight((proportion.value / 100) * windowHeight)
  }

  if (Math.abs(touch.deltaY.value) > 300) {
    updateHeight(closestNumber(anchorHeight.value, height.value))
  } else {
    const index = props.anchors.indexOf(proportion.value)
    if (-touch.deltaY.value > 0) {
      const nextIndex = index === props.anchors.length - 1 ? props.anchors.length - 1 : index + 1
      updateHeight(anchorHeight.value[nextIndex])
    } else {
      const nextIndex = index === 0 ? 0 : index - 1
      updateHeight(anchorHeight.value[nextIndex])
    }
  }
  proportion.value = props.anchors[anchorHeight.value.indexOf(height.value)]
}

function updateHeight(value) {
  height.value = value
  emit('change', value)
}

function closestNumber(arr, num) {
  if (!arr || arr.length === 0) {
    return null
  }

  let closest = arr[0]
  let minDiff = Math.abs(arr[0] - num)

  for (let i = 1; i < arr.length; i++) {
    const diff = Math.abs(arr[i] - num)
    if (diff < minDiff) {
      minDiff = diff
      closest = arr[i]
    }
  }

  return closest
}
</script>

<style lang="less" scoped>
.action-sheet {
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  touch-action: none;
  will-change: transform;

  // 解决过渡动画底部高度间隙问题
  &::after {
    position: absolute;
    bottom: -100vh;
    display: block;
    width: 100%;
    height: 100vh;
    content: '';
    background-color: inherit;
  }

  &::before {
    content: '';
    width: 60rpx;
    height: 8rpx;
    background-color: #999;
    position: absolute;
    top: 20rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 7rpx;
  }
}

.header {
  background-color: #fff;
  height: 30px;
  border-radius: 24rpx 24rpx 0 0;
  border-bottom: 2rpx solid #f5f5f5;
}
.content {
  flex: 1;
  min-width: 0;
  min-height: 0;
  overflow: auto;
  background-color: #fff;
}
</style>
