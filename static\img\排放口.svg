<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="blueToViolet" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8E44AD;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="pipeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#5DADE2;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3498DB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980B9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 排放管道 -->
  <rect x="16" y="48" width="80" height="32" fill="url(#pipeGradient)" stroke="#2C3E50" stroke-width="2"/>
  
  <!-- 管道内壁 -->
  <rect x="20" y="52" width="72" height="24" fill="#3498DB" opacity="0.3"/>
  
  <!-- 排放口开口 -->
  <ellipse cx="96" cy="64" rx="16" ry="20" fill="url(#blueToViolet)" stroke="#2C3E50" stroke-width="2"/>
  
  <!-- 排放口内部 -->
  <ellipse cx="96" cy="64" rx="12" ry="16" fill="#2C3E50" opacity="0.8"/>
  
  <!-- 水流效果 -->
  <path d="M 24 60 Q 32 56 40 60 Q 48 64 56 60 Q 64 56 72 60 Q 80 64 88 60" 
        stroke="#E8F4FD" stroke-width="3" fill="none" opacity="0.8"/>
  <path d="M 28 68 Q 36 64 44 68 Q 52 72 60 68 Q 68 64 76 68 Q 84 72 92 68" 
        stroke="#E8F4FD" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 排放水流 -->
  <path d="M 112 64 Q 116 60 120 64 Q 124 68 128 64" 
        stroke="#3498DB" stroke-width="4" fill="none" opacity="0.7"/>
  <path d="M 112 68 Q 116 64 120 68 Q 124 72 128 68" 
        stroke="#3498DB" stroke-width="3" fill="none" opacity="0.5"/>
  <path d="M 112 60 Q 116 56 120 60 Q 124 64 128 60" 
        stroke="#3498DB" stroke-width="3" fill="none" opacity="0.5"/>
  
  <!-- 支撑结构 -->
  <rect x="16" y="44" width="8" height="8" fill="url(#blueToViolet)" stroke="#2C3E50" stroke-width="1"/>
  <rect x="16" y="76" width="8" height="8" fill="url(#blueToViolet)" stroke="#2C3E50" stroke-width="1"/>
  <rect x="40" y="44" width="8" height="8" fill="url(#blueToViolet)" stroke="#2C3E50" stroke-width="1"/>
  <rect x="40" y="76" width="8" height="8" fill="url(#blueToViolet)" stroke="#2C3E50" stroke-width="1"/>
  
  <!-- 标识符号 -->
  <circle cx="32" cy="32" r="8" fill="url(#blueToViolet)" stroke="#2C3E50" stroke-width="1"/>
  <text x="32" y="36" text-anchor="middle" fill="#FFFFFF" font-family="Arial" font-size="10" font-weight="bold">出</text>
</svg>
