<template>
  <div class="flex f-column all relative">
    <div class="pad-24 f-1 overflow-auto" v-if="feedbackList.length">
      <template v-for="item in feedbackList" :key="item.FeedbackID">
        <div class="back-white border-R16 box-shadow relative mar-B24 pad-24 f-xy-center" @click="handleFeedbackClick(item)">
          <div class="f-1">
            <div class="fon-S30 text-nowrap-2">{{ item.FeedbackContent }}</div>
            <div class="color-666 fon-S24 mar-T10">{{ item.CreateTime.slice(0, 10).replace(/-/g, '/') }}</div>
          </div>
          <wd-icon name="arrow-right" color="#999" size="22px"></wd-icon>
          <div class="new_message" v-if="item.IsReadByCurrentUser === 0 && item.newestReply"></div>
        </div>
      </template>
    </div>
    <wd-status-tip image="collect" tip="暂无数据" v-else />
    <div class="addIcon f-xy-center"><wd-icon name="add" @click="handleAddFeedbackClick" color="#fff" size="22px"></wd-icon></div>
  </div>
  <wd-toast />
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'
import { cache } from '@/utils/cache.js'

import { feedbackIssueListApi } from '@/services/model/feedback.issue.js'

const toast = useToast()

onShow(() => getFeedbackIssueList())

const feedbackList = ref([])
async function getFeedbackIssueList() {
  try {
    const userInfo = cache.get('userInfo')
    const { data } = await feedbackIssueListApi(userInfo.id)
    feedbackList.value = data
  } catch (error) {
    toast.error('获取反馈列表失败')
  }
}

function handleAddFeedbackClick() {
  uni.navigateTo({ url: '/pages/mine/subpage/feedback/feedback.found' })
}

function handleFeedbackClick(item) {
  uni.navigateTo({ url: '/pages/mine/subpage/feedback/feedback.detail?id=' + item.FeedbackID })
}
</script>

<style lang="less" scoped>
.addIcon {
  width: 100rpx;
  height: 100rpx;
  position: absolute;
  background-color: #34d19d;
  right: 38rpx;
  bottom: 100rpx;
  border-radius: 50%;
  box-shadow: 2px 2px 10px #666;
}
.new_message {
  width: 16rpx;
  height: 16rpx;
  background-color: #ff0000;
  border-radius: 100%;
  position: absolute;
  top: 16rpx;
  right: 16rpx;
}
</style>
