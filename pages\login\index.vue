<template>
  <div class="wrap all back-white">
    <!-- 增强背景装饰 -->
    <div class="bg-enhancements">
      <!-- 浮动圆圈 -->
      <div class="floating-circles">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
        <div class="circle circle-4"></div>
        <div class="circle circle-5"></div>
        <div class="circle circle-6"></div>
      </div>

      <!-- 粒子效果 -->
      <div class="particles">
        <div class="particle particle-1"></div>
        <div class="particle particle-2"></div>
        <div class="particle particle-3"></div>
        <div class="particle particle-4"></div>
        <div class="particle particle-5"></div>
        <div class="particle particle-6"></div>
        <div class="particle particle-7"></div>
        <div class="particle particle-8"></div>
      </div>

      <!-- 光线效果 -->
      <div class="light-rays">
        <div class="ray ray-1"></div>
        <div class="ray ray-2"></div>
        <div class="ray ray-3"></div>
        <div class="ray ray-4"></div>
      </div>

      <!-- 动态渐变背景 -->
      <div class="dynamic-gradient"></div>

      <!-- 渐变遮罩 -->
      <div class="gradient-overlay"></div>
    </div>

    <!-- Logo和标题区域 -->
    <div class="header-section">
      <div class="logo-container">
        <image src="/static/img/AppIcon.jpg" class="logo" />
      </div>
      <div class="title-section">
        <div class="title">快捷登录</div>
        <div class="title_for">选择您的登录方式</div>
      </div>
    </div>

    <!-- 主要登录方式 - 微信登录 -->
    <div class="main-login-section">
      <div class="wechat-login-btn" @click="handleWXLogin" @touchstart="createRipple">
        <div class="wechat-icon">
          <image src="/static/img/WX.png" class="wechat-img" />
        </div>
        <div class="wechat-text">
          <div class="wechat-title">微信授权登录</div>
          <div class="wechat-subtitle">快速安全登录</div>
        </div>
      </div>
    </div>

    <!-- 其他登录方式 -->
    <div class="bot f-xy-center">
      <div class="divider-line"></div>
      <div class="fon-S24 pad-X12 color-333">其他登录方式</div>
      <div class="divider-line"></div>
    </div>
    <div class="other-login-methods">
      <div class="login-method-item" @click="showPasswordLogin" @touchstart="createIconRipple">
        <div class="method-icon password-icon">
          <text class="icon-text">账</text>
        </div>
        <div class="method-text">账号密码</div>
      </div>
    </div>
  </div>

  <!-- 账号密码登录弹窗 -->
  <wd-popup v-model="passwordLoginOpen" :close-on-click-modal="false" :closable="true">
    <div class="password-login-popup">
      <!-- 标题区域 -->
      <div class="popup-header">
        <div class="popup-title">账号密码登录</div>
        <div class="popup-subtitle">忘记密码请联系管理员</div>
      </div>

      <!-- 内容区域 -->
      <div class="popup-content">
        <div class="modern-plate">
          <div class="modern-input-box">
            <div class="input-label">账号</div>
            <div class="input-wrapper">
              <wd-input type="text" :focus-when-clear="false" v-model="loginFormData.username" clearable placeholder="请输入账号" />
            </div>
          </div>
          <div class="modern-input-box">
            <div class="input-label">密码</div>
            <div class="input-wrapper">
              <wd-input v-model="loginFormData.password" clearable show-password placeholder="请输入密码" />
            </div>
          </div>
        </div>

        <!-- 登录按钮 -->
        <div class="modern-btn" @click="handlePasswordLogin" @touchstart="createPopupRipple" :class="{ 'btn-disabled': isPasswordLoginDisabled }">
          <span class="btn-text">登录</span>
        </div>
      </div>
    </div>
  </wd-popup>

  <!-- 账号绑定弹窗 -->
  <wd-popup v-model="bindingPupupOpen" :close-on-click-modal="false" :closable="true">
    <div class="binding-popup">
      <!-- 标题区域 -->
      <div class="popup-header">
        <div class="popup-title">绑定账号</div>
        <div class="popup-subtitle">请绑定您的账号以完成登录</div>
      </div>

      <!-- 内容区域 -->
      <div class="popup-content">
        <div class="modern-plate">
          <div class="modern-input-box">
            <div class="input-label">账号</div>
            <div class="input-wrapper">
              <wd-input type="text" :focus-when-clear="false" v-model="formData.username" clearable placeholder="请输入账号" />
            </div>
          </div>
          <div class="modern-input-box">
            <div class="input-label">密码</div>
            <div class="input-wrapper">
              <wd-input v-model="formData.password" clearable show-password placeholder="请输入密码" />
            </div>
          </div>
        </div>

        <!-- 绑定按钮 -->
        <div class="modern-btn" @click="bindingAccount" @touchstart="createPopupRipple" :class="{ 'btn-disabled': isAllow }">
          <span class="btn-text">绑定账号</span>
        </div>
      </div>
    </div>
  </wd-popup>

  <wd-toast />
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'

import { login, WXlogin, WXBinding } from '@/services/model/login'
import { cache } from '@/utils/cache'
import { useToast } from 'wot-design-uni'
import debounce from 'lodash/debounce'

const toast = useToast()
const bindingPupupOpen = ref(false)
const passwordLoginOpen = ref(false)
const formData = reactive({ username: null, password: null, code: null })
const loginFormData = reactive({ username: null, password: null })
const isAllow = computed(() => !(formData.username && formData.password))
const isPasswordLoginDisabled = computed(() => !(loginFormData.username && loginFormData.password))
const isPastDue = ref(false)

onLoad((e) => {
  isPastDue.value = cache.get('token') ? true : false
})

// 创建水波效果
function createRipple(event) {
  const button = event.currentTarget
  const rect = button.getBoundingClientRect()
  const size = Math.max(rect.width, rect.height)
  const x = event.touches ? event.touches[0].clientX - rect.left : event.clientX - rect.left
  const y = event.touches ? event.touches[0].clientY - rect.top : event.clientY - rect.top

  // 创建水波元素
  const ripple = document.createElement('span')
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    left: ${x - size / 2}px;
    top: ${y - size / 2}px;
    width: ${size}px;
    height: ${size}px;
    pointer-events: none;
  `

  button.appendChild(ripple)

  // 动画结束后移除元素
  setTimeout(() => {
    ripple.remove()
  }, 600)
}

// 为图标创建水波效果
function createIconRipple(event) {
  const icon = event.currentTarget.querySelector('.method-icon')
  if (!icon) return

  const rect = icon.getBoundingClientRect()
  const size = Math.max(rect.width, rect.height)

  // 创建水波元素
  const ripple = document.createElement('span')
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: scale(0);
    animation: ripple-animation 0.4s linear;
    left: 50%;
    top: 50%;
    width: ${size}px;
    height: ${size}px;
    margin-left: -${size / 2}px;
    margin-top: -${size / 2}px;
    pointer-events: none;
  `

  icon.appendChild(ripple)

  // 动画结束后移除元素
  setTimeout(() => {
    ripple.remove()
  }, 400)
}

// 为弹窗按钮创建水波效果
function createPopupRipple(event) {
  const button = event.currentTarget
  const rect = button.getBoundingClientRect()
  const size = Math.max(rect.width, rect.height)
  const x = event.touches ? event.touches[0].clientX - rect.left : event.clientX - rect.left
  const y = event.touches ? event.touches[0].clientY - rect.top : event.clientY - rect.top

  // 创建水波元素
  const ripple = document.createElement('span')
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ripple-animation 0.5s linear;
    left: ${x - size / 2}px;
    top: ${y - size / 2}px;
    width: ${size}px;
    height: ${size}px;
    pointer-events: none;
  `

  button.appendChild(ripple)

  // 动画结束后移除元素
  setTimeout(() => {
    ripple.remove()
  }, 500)
}

// 显示账号密码登录弹窗
function showPasswordLogin() {
  // 触觉反馈
  uni.vibrateShort({ type: 'light' })

  passwordLoginOpen.value = true
  // 清空表单数据
  loginFormData.username = null
  loginFormData.password = null
}

// 账号密码登录（弹窗中的登录）
async function handlePasswordLogin() {
  if (isPasswordLoginDisabled.value) {
    toast.error('请输入用户名和密码')
    return
  }

  // 触觉反馈
  uni.vibrateShort({
    type: 'medium'
  })

  try {
    toast.loading('正在登录')
    const { code: resCode, data } = await login(loginFormData)
    toast.close()
    if (resCode === 200) {
      cache.set('token', data.token)
      cache.set('userInfo', data.userInfo)
      toast.success('登录成功')
      passwordLoginOpen.value = false // 关闭弹窗
      setTimeout(() => {
        navigateAfterLogin()
      }, 1000)
    } else {
      toast.error('登录失败，请检查用户名和密码是否正确')
    }
  } catch (error) {
    toast.close()
    toast.error('登录失败')
  }
}

// 登录后导航逻辑
function navigateAfterLogin() {
  let stepNumber = 0
  let lastPage = null
  const pages = getCurrentPages().reverse()
  for (let i = 0; i < pages.length; i++) {
    const page = pages[i]
    if (page.route === 'pages/login/index') {
      stepNumber++
    } else {
      lastPage = page.route
      break
    }
  }

  if ((isPastDue.value && lastPage !== null) || lastPage === 'pages/zone-record/detail') {
    uni.navigateBack({ delta: stepNumber })
  } else {
    uni.reLaunch({ url: '/pages/home/<USER>' })
  }
}

// 微信登录
async function handleWXLogin(type) {
  // 触觉反馈
  uni.vibrateShort({ type: 'medium' })

  const content = type !== '授权绑定' ? '请授权微信登录' : '绑定成功,微信授权登录？'

  const { confirm } = await uni.showModal({ title: '授权提示', content })
  try {
    if (confirm) {
      // 用户点击确认
      uni.vibrateShort({ type: 'medium' })
      toast.loading('正在登录')
      const { code } = await uni.login()
      const { code: resCode, data } = await WXlogin({ code })
      toast.close()
      if (resCode === 200) {
        cache.set('token', data.token)
        cache.set('userInfo', data.userInfo)
        toast.success('登录成功')
        setTimeout(() => {
          navigateAfterLogin()
        }, 1000)
      }
      if (resCode === 400) toast.warning(data)
      if (data === '未绑定账号') bindingPupupOpen.value = true
    }
  } catch (error) {
    toast.close()
    toast.error('登录失败')
  }
}

// 绑定账号
async function bindingAccount() {
  try {
    if (isAllow.value) return toast.error('请输入用户名和密码')

    // 触觉反馈
    uni.vibrateShort({ type: 'medium' })

    toast.loading('正在登录')

    const { code } = await uni.login()
    formData.code = code

    const { code: resCode, data } = await WXBinding(formData)

    if (resCode === 200) {
      toast.close()
      toast.success('绑定成功,请点击登录')
      bindingPupupOpen.value = false
      formData.username = null
      formData.password = null
      formData.code = null
      handleWXLogin('授权绑定')
    } else {
      toast.close()
      toast.error('绑定失败' + data)
    }
  } catch (error) {
    toast.close()
    toast.error('绑定失败')
  }
}
</script>

<style lang="less" scoped>
.wrap {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  position: relative;
  overflow: hidden;
  padding: 120rpx 90rpx 0 90rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/static/img/bg2.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: 0;
  }
}
// 增强背景装饰
.bg-enhancements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;

  // 增强的浮动圆圈
  .floating-circles {
    position: absolute;
    width: 100%;
    height: 100%;

    .circle {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      backdrop-filter: blur(10rpx);
      border: 1rpx solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      &.circle-1 {
        width: 150rpx;
        height: 150rpx;
        top: 10%;
        right: -50rpx;
        animation: floatSlow 12s ease-in-out infinite;
      }

      &.circle-2 {
        width: 100rpx;
        height: 100rpx;
        bottom: 20%;
        left: -30rpx;
        animation: floatSlow 15s ease-in-out infinite reverse;
      }

      &.circle-3 {
        width: 80rpx;
        height: 80rpx;
        top: 40%;
        left: 10%;
        animation: floatSlow 10s ease-in-out infinite;
      }

      &.circle-4 {
        width: 60rpx;
        height: 60rpx;
        bottom: 30%;
        right: 15%;
        animation: floatSlow 18s ease-in-out infinite reverse;
      }

      &.circle-5 {
        width: 40rpx;
        height: 40rpx;
        top: 70%;
        left: 70%;
        animation: floatSlow 14s ease-in-out infinite;
      }

      &.circle-6 {
        width: 30rpx;
        height: 30rpx;
        top: 25%;
        left: 50%;
        animation: floatSlow 16s ease-in-out infinite reverse;
      }
    }
  }

  // 粒子效果
  .particles {
    position: absolute;
    width: 100%;
    height: 100%;

    .particle {
      position: absolute;
      width: 4rpx;
      height: 4rpx;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      animation: particleFloat 20s linear infinite;

      &.particle-1 {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
      }

      &.particle-2 {
        top: 50%;
        left: 80%;
        animation-delay: -5s;
      }

      &.particle-3 {
        top: 80%;
        left: 30%;
        animation-delay: -10s;
      }

      &.particle-4 {
        top: 30%;
        left: 60%;
        animation-delay: -15s;
      }

      &.particle-5 {
        top: 60%;
        left: 20%;
        animation-delay: -3s;
      }

      &.particle-6 {
        top: 40%;
        left: 90%;
        animation-delay: -8s;
      }

      &.particle-7 {
        top: 70%;
        left: 70%;
        animation-delay: -12s;
      }

      &.particle-8 {
        top: 10%;
        left: 40%;
        animation-delay: -18s;
      }
    }
  }

  // 增强的光线效果
  .light-rays {
    position: absolute;
    width: 100%;
    height: 100%;

    .ray {
      position: absolute;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      border-radius: 2rpx;
      filter: blur(1rpx);

      &.ray-1 {
        width: 300rpx;
        height: 3rpx;
        top: 25%;
        left: -150rpx;
        animation: rayMove 20s linear infinite;
      }

      &.ray-2 {
        width: 250rpx;
        height: 2rpx;
        bottom: 35%;
        right: -125rpx;
        animation: rayMove 25s linear infinite reverse;
      }

      &.ray-3 {
        width: 200rpx;
        height: 2rpx;
        top: 55%;
        left: -100rpx;
        animation: rayMove 30s linear infinite;
      }

      &.ray-4 {
        width: 180rpx;
        height: 1rpx;
        top: 75%;
        right: -90rpx;
        animation: rayMove 35s linear infinite reverse;
      }
    }
  }

  // 动态渐变背景
  .dynamic-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    animation: gradientMove 20s ease-in-out infinite;
  }

  // 渐变遮罩
  .gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: pulseGlow 15s ease-in-out infinite;
  }
}

// Logo和标题区域
.header-section {
  position: relative;
  z-index: 10;
  text-align: center;
  margin-bottom: 60rpx;
  margin-top: 60rpx;

  .logo-container {
    margin-bottom: 48rpx;
    padding: 20rpx;
    animation: fadeInDown 0.8s ease-out both;
    display: flex;
    justify-content: center;
    align-items: center;

    .logo {
      width: 140rpx;
      height: 140rpx;
      border-radius: 28rpx;
      box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2), 0 6rpx 16rpx rgba(0, 0, 0, 0.12), inset 0 0 0 2rpx rgba(255, 255, 255, 0.1);
      border: none;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10rpx);
      padding: 4rpx;
      display: block;
      margin: 0 auto;

      &:hover {
        transform: scale(1.08) rotate(3deg);
        box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.25), 0 8rpx 20rpx rgba(0, 0, 0, 0.15), inset 0 0 0 2rpx rgba(255, 255, 255, 0.2);
      }
    }
  }

  .title-section {
    .title {
      font-size: 48rpx;
      font-weight: 700;
      margin-bottom: 12rpx;
      animation: fadeInUp 0.8s ease-out 0.2s both;
      background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
      letter-spacing: 1rpx;
    }

    .title_for {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
      animation: fadeInUp 1s ease-out 0.4s both;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }
  }
}

// 表单区域
.plate {
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  margin-bottom: 40rpx;
  animation: slideInUp 0.8s ease-out 0.3s both;

  .input-box {
    margin-bottom: 32rpx;
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }

    .fon-S36 {
      margin-bottom: 12rpx;
      color: #1f2937;
      font-weight: 500;
      font-size: 30rpx;
      position: relative;
    }

    .input-box-right {
      :deep(.wd-input) {
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
        background: #ffffff;
        border: 1rpx solid #e5e7eb;
        border-radius: 8rpx;
        padding: 0 16rpx;
        font-size: 30rpx;
        transition: all 0.2s ease;
        box-shadow: none;
        min-height: 88rpx;
        height: 88rpx;
        line-height: 1.5;
        color: #374151;

        &:focus-within {
          border-color: #3b82f6;
          background: #ffffff;
          box-shadow: 0 0 0 3rpx rgba(59, 130, 246, 0.1);
          outline: none;
        }

        &::placeholder {
          color: #9ca3af;
          font-size: 30rpx;
        }
      }

      :deep(.wd-input__icon),
      :deep(.wd-input__clear) {
        background: transparent;
        color: #6b7280;
      }

      :deep(.wd-input__suffix) {
        color: #3b82f6;
      }
    }
  }
}

// 登录按钮
.btn {
  position: relative;
  width: 100%;
  background: #3b82f6;
  border-radius: 8rpx;
  padding: 0;
  text-align: center;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1), 0 1rpx 2rpx rgba(0, 0, 0, 0.06);
  margin-bottom: 40rpx;
  animation: slideInUp 1s ease-out 0.6s both;

  &:active {
    transform: translateY(1rpx);
    box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  }

  &:hover {
    background: #2563eb;
    box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
  }

  &.btn-disabled {
    background: #9ca3af;
    box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
    cursor: not-allowed;

    &:active {
      transform: none;
    }

    &:hover {
      background: #9ca3af;
      box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
    }

    .btn-ripple {
      display: none;
    }
  }

  .btn-text {
    position: relative;
    z-index: 2;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
    letter-spacing: 0.5rpx;
    padding: 24rpx;
    display: block;
  }

  // 简化的波纹效果
  .btn-ripple {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
    z-index: 1;
    animation: rippleMove 4s linear infinite;
  }
}

// 主要登录区域 - 微信登录
.main-login-section {
  position: relative;
  z-index: 10;
  margin-bottom: 60rpx;
  animation: slideInUp 0.8s ease-out 0.6s both;

  .wechat-login-btn {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    backdrop-filter: blur(30rpx);
    border-radius: 20rpx;
    padding: 56rpx 40rpx;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15), 0 8rpx 24rpx rgba(0, 0, 0, 0.08), inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    // 光扫效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.6s ease;
    }

    // 水波效果
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      transition: width 0.6s ease, height 0.6s ease, opacity 0.6s ease;
      opacity: 0;
      pointer-events: none;
    }

    // 点击时的水波动画
    &:active::after {
      width: 600rpx;
      height: 600rpx;
      opacity: 1;
      transition: width 0.3s ease, height 0.3s ease, opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-4rpx) scale(1.02);
      box-shadow: 0 30rpx 80rpx rgba(0, 0, 0, 0.2), 0 12rpx 32rpx rgba(0, 0, 0, 0.12), inset 0 1rpx 0 rgba(255, 255, 255, 0.9);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(-2rpx) scale(1.01);
    }

    .wechat-icon {
      margin-right: 40rpx;
      position: relative;

      .wechat-img {
        width: 88rpx;
        height: 88rpx;
        filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
        transition: all 0.3s ease;
      }
    }

    .wechat-text {
      flex: 1;

      .wechat-title {
        font-size: 40rpx;
        font-weight: 700;
        background: linear-gradient(135deg, #1f2937, #4b5563);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 8rpx;
        letter-spacing: 0.5rpx;
      }

      .wechat-subtitle {
        font-size: 28rpx;
        color: #6b7280;
        font-weight: 500;
        opacity: 0.8;
      }
    }

    &:hover .wechat-icon .wechat-img {
      transform: scale(1.1) rotate(5deg);
    }
  }
}

// 其他登录方式
.other-login-methods {
  display: flex;
  justify-content: center;
  gap: 48rpx;
  margin-top: 24rpx;
  animation: fadeInUp 1.2s ease-out 1s both;

  .login-method-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4rpx);
    }

    .method-icon {
      width: 88rpx;
      height: 88rpx;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20rpx;
      box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2rpx solid rgba(255, 255, 255, 0.5);
      position: relative;
      overflow: hidden;

      .icon {
        width: 48rpx;
        height: 48rpx;
      }

      // 图标水波效果
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        transform: translate(-50%, -50%);
        transition: width 0.4s ease, height 0.4s ease, opacity 0.4s ease;
        opacity: 0;
        pointer-events: none;
      }

      &:active::after {
        width: 200rpx;
        height: 200rpx;
        opacity: 1;
        transition: width 0.2s ease, height 0.2s ease, opacity 0.2s ease;
      }

      &.password-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);

        .icon-text {
          color: #ffffff;
          font-size: 34rpx;
          font-weight: 700;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
          position: relative;
          z-index: 1;
        }

        &::after {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }

    .method-text {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
    }

    &:hover {
      .method-icon {
        transform: translateY(-2rpx) scale(1.05);
        box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.2);

        &.password-icon {
          box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
        }
      }

      .method-text {
        color: rgba(255, 255, 255, 1);
        transform: translateY(-2rpx);
      }
    }
  }
}

// 底部区域
.bot {
  position: relative;
  z-index: 10;
  margin-top: 60rpx;
  margin-bottom: 60rpx;
  animation: fadeInUp 1.2s ease-out 1.2s both;

  .divider-line {
    width: 25%;
    height: 2rpx;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  }

  .fon-S24 {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    font-size: 28rpx;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }
}

// 动画定义
@keyframes floatSlow {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-30rpx) rotate(180deg);
  }
}

@keyframes rayMove {
  0% {
    transform: translateX(-100%) rotate(15deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100vw) rotate(15deg);
    opacity: 0;
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100rpx) translateX(100rpx) scale(1);
    opacity: 0;
  }
}

@keyframes gradientMove {
  0%,
  100% {
    transform: translateX(-50%) translateY(-50%) rotate(0deg);
  }
  50% {
    transform: translateX(-30%) translateY(-30%) rotate(180deg);
  }
}

@keyframes pulseGlow {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes titleGlow {
  0% {
    filter: drop-shadow(0 0 10rpx rgba(77, 128, 240, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 20rpx rgba(77, 128, 240, 0.6));
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-40rpx) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes ripple-animation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes popupSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(40rpx);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05) translateY(-10rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(60rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes rippleMove {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes buttonGlow {
  0%,
  100% {
    box-shadow: 0 0 20rpx rgba(77, 128, 240, 0.4);
  }
  50% {
    box-shadow: 0 0 40rpx rgba(77, 128, 240, 0.8);
  }
}

@keyframes lineGlow {
  0%,
  100% {
    opacity: 0.3;
    transform: scaleX(0.5);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
}

@keyframes dotPulse {
  0%,
  100% {
    opacity: 0.3;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.2);
  }
}

// 账号密码登录弹窗样式
.password-login-popup {
  width: 640rpx !important;
  border-radius: 24rpx;
  padding: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 32rpx 80rpx rgba(0, 0, 0, 0.15), 0 12rpx 32rpx rgba(0, 0, 0, 0.1), inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  position: relative;
  animation: popupSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);

  // 弹窗背景装饰
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.1;
  }

  // 标题区域样式
  .popup-header {
    position: relative;
    padding: 48rpx 40rpx 32rpx;
    text-align: center;
    z-index: 2;

    .popup-title {
      font-size: 40rpx;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 12rpx;
      background: linear-gradient(135deg, #1f2937, #4b5563);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .popup-subtitle {
    font-size: 26rpx;
    color: #6b7280;
    font-weight: 500;
    opacity: 0.8;
  }

  // 内容区域样式
  .popup-content {
    padding: 0 40rpx 48rpx;
    position: relative;
    z-index: 2;
  }

  // 现代化表单样式
  .modern-plate {
    margin-bottom: 40rpx;
  }

  .modern-input-box {
    margin-bottom: 32rpx;

    .input-label {
      font-size: 32rpx;
      font-weight: 600;
      color: #374151;
      margin-bottom: 16rpx;
      padding-left: 4rpx;
    }

    .input-wrapper {
      position: relative;

      :deep(.wd-input) {
        background: rgba(255, 255, 255, 0.8);
        border: 2rpx solid rgba(0, 0, 0, 0.1);
        border-radius: 16rpx;
        padding: 24rpx 20rpx;
        font-size: 30rpx;
        transition: all 0.3s ease;

        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
          background: rgba(255, 255, 255, 0.95);
        }
      }
    }
  }

  // 现代化按钮样式
  .modern-btn {
    width: 100%;
    height: 96rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
    border: none;

    .btn-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #ffffff;
      position: relative;
      z-index: 1;
    }

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    &.btn-disabled {
      background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 账号绑定弹窗样式
.binding-popup {
  width: 640rpx !important;
  border-radius: 24rpx;
  padding: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 32rpx 80rpx rgba(0, 0, 0, 0.15), 0 12rpx 32rpx rgba(0, 0, 0, 0.1), inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  position: relative;
  animation: popupSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);

  // 弹窗背景装饰
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.1;
  }

  // 继承所有弹窗通用样式
  .popup-header {
    position: relative;
    padding: 48rpx 40rpx 32rpx;
    text-align: center;
    z-index: 2;

    .popup-title {
      font-size: 40rpx;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 12rpx;
      background: linear-gradient(135deg, #1f2937, #4b5563);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .popup-subtitle {
    font-size: 26rpx;
    color: #6b7280;
    font-weight: 500;
    opacity: 0.8;
  }

  .popup-content {
    padding: 0 40rpx 48rpx;
    position: relative;
    z-index: 2;
  }

  .modern-plate {
    margin-bottom: 40rpx;
  }

  .modern-input-box {
    margin-bottom: 32rpx;

    .input-label {
      font-size: 32rpx;
      font-weight: 600;
      color: #374151;
      margin-bottom: 16rpx;
      padding-left: 4rpx;
    }

    .input-wrapper {
      position: relative;

      :deep(.wd-input) {
        background: rgba(255, 255, 255, 0.8);
        border: 2rpx solid rgba(0, 0, 0, 0.1);
        border-radius: 16rpx;
        padding: 24rpx 20rpx;
        font-size: 30rpx;
        transition: all 0.3s ease;

        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
          background: rgba(255, 255, 255, 0.95);
        }
      }
    }
  }

  .modern-btn {
    width: 100%;
    height: 96rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
    border: none;

    .btn-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #ffffff;
      position: relative;
      z-index: 1;
    }

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    &.btn-disabled {
      background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 通用图标样式
.icon {
  width: 100rpx;
  height: 100rpx;
}
</style>
