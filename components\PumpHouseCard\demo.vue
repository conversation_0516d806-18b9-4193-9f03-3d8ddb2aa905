<template>
  <div class="demo-container">
    <div class="demo-header">
      <h2>泵房卡片组件演示</h2>
      <p>展示不同版本的卡片组件设计</p>
    </div>

    <!-- 版本切换 -->
    <div class="version-selector">
      <div class="selector-title">选择版本：</div>
      <div class="version-tabs">
        <div 
          v-for="version in versions" 
          :key="version.key"
          class="version-tab"
          :class="{ active: currentVersion === version.key }"
          @click="currentVersion = version.key"
        >
          {{ version.name }}
        </div>
      </div>
    </div>

    <!-- 卡片展示 -->
    <div class="cards-container">
      <div class="cards-grid">
        <component 
          :is="currentComponent"
          v-for="(item, index) in demoData" 
          :key="index"
          :item="item" 
          :index="index" 
          @click="handleCardClick" 
        />
      </div>
    </div>

    <!-- 点击信息显示 -->
    <div class="click-info" v-if="clickedItem">
      <h3>点击的泵房信息：</h3>
      <pre>{{ JSON.stringify(clickedItem, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import OriginalCard from './index.vue'
import ModernCard from './modern.vue'
import EnhancedCard from './enhanced.vue'

// 当前选择的版本
const currentVersion = ref('enhanced')

// 版本配置
const versions = [
  { key: 'original', name: '原始版本', component: OriginalCard },
  { key: 'modern', name: '现代化版本', component: ModernCard },
  { key: 'enhanced', name: '增强版本', component: EnhancedCard }
]

// 当前组件
const currentComponent = computed(() => {
  const version = versions.find(v => v.key === currentVersion.value)
  return version ? version.component : EnhancedCard
})

// 演示数据
const demoData = ref([
  {
    PumpHouseName: '中央泵房A区',
    PumpRoomNumber: 'PH001',
    ProgressStatus: '正常运行',
    status: '正常',
    zoneCode: 'ZN001',
    address: '深圳市福田区中心城片区福保街道福保社区',
    updateTime: new Date(Date.now() - 1000 * 60 * 30).toISOString() // 30分钟前
  },
  {
    PumpHouseName: '东区供水泵房',
    PumpRoomNumber: 'PH002',
    ProgressStatus: '设备维护',
    status: '维护',
    zoneCode: 'ZN002',
    address: '深圳市福田区梅林片区梅林街道梅林社区',
    updateTime: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() // 2小时前
  },
  {
    PumpHouseName: '西区加压泵房',
    PumpRoomNumber: 'PH003',
    ProgressStatus: '高负荷运行',
    status: '运行',
    zoneCode: 'ZN003',
    address: '深圳市福田区香蜜片区香蜜湖街道香蜜社区',
    updateTime: new Date(Date.now() - 1000 * 60 * 10).toISOString() // 10分钟前
  },
  {
    PumpHouseName: '南区备用泵房',
    PumpRoomNumber: 'PH004',
    ProgressStatus: '设备故障',
    status: '故障',
    zoneCode: 'ZN004',
    address: '深圳市福田区福中片区福中街道福中社区',
    updateTime: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString() // 6小时前
  },
  {
    PumpHouseName: '北区辅助泵房',
    PumpRoomNumber: 'PH005',
    ProgressStatus: '暂停使用',
    status: '停用',
    zoneCode: 'ZN005',
    address: '深圳市福田区福东片区福东街道福东社区',
    updateTime: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() // 1天前
  }
])

// 点击的项目
const clickedItem = ref(null)

// 处理卡片点击
function handleCardClick(item) {
  clickedItem.value = item
  console.log('点击了泵房:', item)
}
</script>

<style lang="less" scoped>
.demo-container {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 40rpx;
  
  h2 {
    font-size: 48rpx;
    font-weight: 700;
    color: white;
    margin: 0 0 16rpx 0;
  }
  
  p {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
  }
}

.version-selector {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.selector-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20rpx;
}

.version-tabs {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.version-tab {
  padding: 16rpx 32rpx;
  background: rgba(248, 250, 252, 0.8);
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(64, 169, 255, 0.1);
    border-color: rgba(64, 169, 255, 0.3);
    color: #40a9ff;
  }
  
  &.active {
    background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
    border-color: transparent;
    color: white;
    box-shadow: 0 4rpx 16rpx rgba(64, 169, 255, 0.3);
  }
}

.cards-container {
  margin-bottom: 40rpx;
}

.cards-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.click-info {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  
  h3 {
    font-size: 32rpx;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 20rpx 0;
  }
  
  pre {
    background: rgba(248, 250, 252, 0.8);
    border-radius: 12rpx;
    padding: 20rpx;
    font-size: 22rpx;
    color: #4a5568;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
    border: 1rpx solid rgba(0, 0, 0, 0.06);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .demo-container {
    padding: 24rpx;
  }
  
  .demo-header h2 {
    font-size: 40rpx;
  }
  
  .version-selector {
    padding: 24rpx;
  }
  
  .version-tab {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
  }
  
  .click-info {
    padding: 24rpx;
  }
}
</style>
