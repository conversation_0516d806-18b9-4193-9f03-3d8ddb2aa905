<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#overviewGradient)" stroke="url(#overviewBorder)" stroke-width="2"/>
  
  <!-- 总览图标 -->
  <g transform="translate(12, 12)">
    <!-- 仪表盘外圈 -->
    <circle cx="20" cy="20" r="16" fill="none" stroke="white" stroke-width="2.5" opacity="0.9"/>
    
    <!-- 仪表盘刻度 -->
    <g stroke="white" stroke-width="2" opacity="0.7">
      <!-- 主要刻度 -->
      <line x1="20" y1="6" x2="20" y2="10" />
      <line x1="34" y1="20" x2="30" y2="20" />
      <line x1="20" y1="34" x2="20" y2="30" />
      <line x1="6" y1="20" x2="10" y2="20" />
      
      <!-- 次要刻度 -->
      <line x1="31.31" y1="8.69" x2="29.9" y2="10.1" opacity="0.5"/>
      <line x1="31.31" y1="31.31" x2="29.9" y2="29.9" opacity="0.5"/>
      <line x1="8.69" y1="31.31" x2="10.1" y2="29.9" opacity="0.5"/>
      <line x1="8.69" y1="8.69" x2="10.1" y2="10.1" opacity="0.5"/>
    </g>
    
    <!-- 数据扇形区域 -->
    <path d="M 20 20 L 20 6 A 14 14 0 0 1 31.31 8.69 Z" fill="white" opacity="0.3"/>
    <path d="M 20 20 L 31.31 8.69 A 14 14 0 0 1 34 20 Z" fill="white" opacity="0.2"/>
    <path d="M 20 20 L 34 20 A 14 14 0 0 1 31.31 31.31 Z" fill="white" opacity="0.15"/>
    
    <!-- 指针 -->
    <line x1="20" y1="20" x2="28" y2="12" stroke="white" stroke-width="3" stroke-linecap="round"/>
    
    <!-- 中心圆 -->
    <circle cx="20" cy="20" r="3" fill="white"/>
    <circle cx="20" cy="20" r="1.5" fill="url(#overviewGradient)"/>
    
    <!-- 数据点装饰 -->
    <circle cx="20" cy="8" r="1.5" fill="white" opacity="0.8"/>
    <circle cx="32" cy="20" r="1.5" fill="white" opacity="0.6"/>
    <circle cx="20" cy="32" r="1.5" fill="white" opacity="0.4"/>
    <circle cx="8" cy="20" r="1.5" fill="white" opacity="0.5"/>
  </g>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="overviewGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9b59b6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8e44ad;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="overviewBorder" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8e44ad;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7d3c98;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
