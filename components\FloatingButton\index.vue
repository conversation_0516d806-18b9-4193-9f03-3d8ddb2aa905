<template>
  <!-- 微信小程序拖拽模式 - 简化事件处理 -->
  <movable-area v-if="visible && draggable" class="floating-movable-area" :style="movableAreaStyle">
    <movable-view
      class="floating-button f-xy-center box-shadow pointer"
      :class="[typeof size === 'string' ? `floating-button--${size}` : 'floating-button--custom', 'floating-button--draggable', customClass, { 'floating-button--disabled': disabled }]"
      :style="[buttonStyle, customSizeStyle]"
      direction="all"
      :x="currentX"
      :y="currentY"
      :damping="20"
      :friction="2"
      :out-of-bounds="outOfBounds"
      :disabled="disabled"
      :scale="false"
      :animation="false"
      @change="handlePositionChange"
      @tap="handleDirectClick"
    >
      <slot>
        <wd-icon :name="icon" :size="iconSize" :color="iconColor" />
      </slot>
    </movable-view>
  </movable-area>

  <!-- 固定位置模式 -->
  <view
    v-else-if="visible"
    class="floating-button f-xy-center box-shadow pointer"
    :class="[`floating-button--${position}`, typeof size === 'string' ? `floating-button--${size}` : 'floating-button--custom', customClass, { 'floating-button--disabled': disabled }]"
    :style="[buttonStyle, customSizeStyle]"
    @tap="handleDirectClick"
    @click="handleDirectClick"
  >
    <slot>
      <wd-icon :name="icon" :size="iconSize" :color="iconColor" />
    </slot>
  </view>
</template>

<script setup>
import { computed, ref, onUnmounted } from 'vue'

const props = defineProps({
  // 按钮位置：right-bottom, right-top, left-bottom, left-top（非拖拽模式）
  position: {
    type: String,
    default: 'right-bottom'
  },
  // 按钮大小：可以是预设值或自定义尺寸
  size: {
    type: [String, Number],
    default: 'medium'
  },
  // 图标名称
  icon: {
    type: String,
    default: 'add'
  },
  // 图标颜色
  iconColor: {
    type: String,
    default: '#ffffff'
  },
  // 背景颜色
  backgroundColor: {
    type: String,
    default: '#4d63e0'
  },
  // 自定义样式类
  customClass: {
    type: String,
    default: ''
  },
  // 距离边缘的距离（非拖拽模式）
  offset: {
    type: Object,
    default: () => ({ x: 32, y: 32 })
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否显示
  visible: {
    type: Boolean,
    default: true
  },
  // 是否可拖拽
  draggable: {
    type: Boolean,
    default: false
  },
  // 拖拽初始位置
  initialPosition: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
  // 拖拽阻尼系数
  damping: {
    type: Number,
    default: 1
  },
  // 拖拽摩擦系数
  friction: {
    type: Number,
    default: 1
  },
  // 是否可以超出边界
  outOfBounds: {
    type: Boolean,
    default: false
  },
  // 可拖拽区域的边界
  bounds: {
    type: Object,
    default: () => ({ top: 0, right: 0, bottom: 0, left: 0 })
  }
})

const emit = defineEmits(['click', 'positionChange', 'dragStart', 'dragEnd'])

// 拖拽相关状态
const currentX = ref(props.initialPosition.x)
const currentY = ref(props.initialPosition.y)
const isDragging = ref(false)
const lastChangeTime = ref(0) // 最后一次位置变化时间
const clickDelay = 200 // 点击延迟时间（毫秒）

// 计算图标大小
const iconSize = computed(() => {
  // 如果 size 是数字，直接使用
  if (typeof props.size === 'number') {
    return `${props.size}px`
  }

  // 预设尺寸映射
  const sizeMap = {
    small: '20px',
    medium: '24px',
    large: '28px'
  }
  return sizeMap[props.size] || sizeMap.medium
})

// 计算自定义尺寸样式
const customSizeStyle = computed(() => {
  // 如果 size 是数字，返回自定义尺寸样式
  if (typeof props.size === 'number') {
    return {
      width: `${props.size}rpx`,
      height: `${props.size}rpx`
    }
  }

  // 预设尺寸不需要额外样式
  return {}
})

// 计算可拖拽区域样式
const movableAreaStyle = computed(() => {
  const { bounds } = props
  return {
    position: 'fixed',
    top: `${bounds.top}rpx`,
    left: `${bounds.left}rpx`,
    right: `${bounds.right}rpx`,
    bottom: `${bounds.bottom}rpx`,
    width: `calc(100vw - ${bounds.left + bounds.right}rpx)`,
    height: `calc(100vh - ${bounds.top + bounds.bottom}rpx)`,
    zIndex: 999,
    pointerEvents: 'none'
  }
})

// 计算按钮样式
const buttonStyle = computed(() => {
  const style = {
    backgroundColor: props.disabled ? '#d9d9d9' : props.backgroundColor,
    pointerEvents: 'auto'
  }

  // 如果不是拖拽模式，设置固定位置
  if (!props.draggable) {
    const [vertical, horizontal] = props.position.split('-')
    if (vertical === 'top') {
      style.top = `${props.offset.y}rpx`
    } else {
      style.bottom = `${props.offset.y}rpx`
    }

    if (horizontal === 'left') {
      style.left = `${props.offset.x}rpx`
    } else {
      style.right = `${props.offset.x}rpx`
    }
  }

  return style
})

// 微信小程序专用：简化的事件处理
// 不再使用复杂的触摸事件，只依赖movable-view的change事件和tap事件

// 处理位置变化
function handlePositionChange(e) {
  if (props.disabled) return

  const newX = e.detail.x
  const newY = e.detail.y

  // 检查是否真的发生了位置变化
  const deltaX = Math.abs(newX - currentX.value)
  const deltaY = Math.abs(newY - currentY.value)

  if (deltaX > 3 || deltaY > 3) {
    isDragging.value = true
    lastChangeTime.value = Date.now()
    emit('dragStart')
  }

  currentX.value = newX
  currentY.value = newY

  emit('positionChange', {
    x: newX,
    y: newY
  })
}

// 处理点击事件
function handleClick() {
  if (!props.disabled) {
    uni.vibrateShort({ type: 'medium' })
    emit('click')
  } else {
  }
}

// 直接处理点击事件（微信小程序专用）
function handleDirectClick() {
  if (props.disabled) return

  // 检查是否刚刚发生了拖拽
  const timeSinceLastChange = Date.now() - lastChangeTime.value

  if (isDragging.value && timeSinceLastChange < clickDelay) {
    // 重置拖拽状态
    setTimeout(() => {
      isDragging.value = false
    }, clickDelay)
    return
  }

  // 执行点击
  handleClick()

  // 重置拖拽状态
  isDragging.value = false
}

// 组件卸载时重置状态
onUnmounted(() => {
  isDragging.value = false
  lastChangeTime.value = 0
})
</script>

<style lang="less" scoped>
// 可拖拽区域
.floating-movable-area {
  pointer-events: none;
}

.floating-button {
  border-radius: 50%;
  transition: all 0.3s ease;
  border: none;
  outline: none;

  // 悬浮效果
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15), 0 4rpx 8rpx rgba(0, 0, 0, 0.1);

  // 点击动画（仅非拖拽模式）
  &:not(.floating-button--draggable):active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  }

  // 禁用状态
  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;

    &:active {
      transform: none;
    }
  }

  // 大小变体 - 预设尺寸
  &--small {
    width: 96rpx;
    height: 96rpx;
  }

  &--medium {
    width: 112rpx;
    height: 112rpx;
  }

  &--large {
    width: 128rpx;
    height: 128rpx;
  }

  // 自定义尺寸（通过内联样式设置）
  &--custom {
    min-width: 80rpx;
    min-height: 80rpx;
  }

  // 非拖拽模式的固定定位
  &:not(.floating-button--draggable) {
    position: fixed;
    z-index: 999;
  }
}

// 拖拽模式下的 movable-view
movable-view.floating-button {
  position: relative;
  z-index: auto;
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .floating-button {
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3), 0 4rpx 8rpx rgba(0, 0, 0, 0.2);

    &:not(.floating-button--draggable):active {
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.4);
    }
  }
}

// 微信小程序兼容性
/* #ifdef MP-WEIXIN */
.floating-movable-area {
  /* 确保在微信小程序中正确显示 */
  overflow: visible;
  /* 修复微信小程序中的层级问题 */
  z-index: 9999;
}

movable-view.floating-button {
  /* 微信小程序中的特殊处理 */
  will-change: transform;
  /* 确保触摸事件正常响应 */
  touch-action: manipulation;
  /* 防止选中文本 */
  user-select: none;
  -webkit-user-select: none;
  /* 优化渲染性能 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* 微信小程序中的拖拽优化 */
.floating-button--draggable {
  /* 禁用默认的触摸反馈 */
  -webkit-tap-highlight-color: transparent;
  /* 确保拖拽时不会触发长按菜单 */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}
/* #endif */
</style>
