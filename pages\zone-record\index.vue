<template>
  <div class="zone-record-container">
    <!-- 顶部搜索和筛选区域 -->
    <div class="header-section">
      <div class="search-container">
        <div class="search-wrapper">
          <wd-search v-model="params.xqmc" hide-cancel placeholder="搜索小区名称..." custom-class="modern-search" @search="searchKeywordName" />
        </div>
      </div>

      <!-- 筛选器和统计区域 -->
      <div class="filter-stats-section">
        <!-- 筛选器 -->
        <div class="filter-chips">
          <div class="filter-chip" @click="showWaterDeptPicker">
            <span class="chip-label">{{ params.sws === '全部' ? '筛选水务所' : params.sws }}</span>
            <wd-icon name="arrow-down" size="14px" color="#6c7b7f" />
          </div>
          <div class="filter-chip" @click="showPumpRoomPicker">
            <span class="chip-label">{{ params.exist === '全部' || !params.exist ? '是否有泵房' : params.exist }}</span>
            <wd-icon name="arrow-down" size="14px" color="#6c7b7f" />
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-compact">
          <div class="stats-icon">
            <wd-icon name="home" size="16px" color="#ffffff" />
          </div>
          <div class="stats-text">
            <span class="stats-number">{{ total }}</span>
            <span class="stats-label">个小区</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 小区列表 -->
    <div class="content-section">
      <div class="zone-list">
        <template v-for="(item, index) in recordList" :key="index + item.xqbm">
          <div @click="ItemClick(item)" class="zone-item" :class="{ 'item-alternate': index % 2 === 1 }">
            <div class="item-main">
              <div class="item-left">
                <div class="zone-avatar">
                  <div class="avatar-bg">
                    <image src="/static/img/home/<USER>" mode="aspectFit" class="avatar-img" />
                  </div>
                </div>
                <div class="zone-details">
                  <h4 class="zone-title">{{ item.xqmc }}</h4>
                  <div class="zone-meta">
                    <!-- <span class="meta-item">
                      <wd-icon name="location" size="10px" color="#8e9aaf" />
                      {{ item.xqbm }}
                    </span> -->
                    <span class="meta-divider">•</span>
                    <span class="meta-item">{{ item.xqlb }}</span>
                    <span class="meta-divider" v-if="item.hasWaterTankArchive">•</span>
                    <span class="meta-item" style="color: #13c2c2">{{ item.hasWaterTankArchive ? '水池箱' : '' }}</span>
                  </div>
                </div>
              </div>
              <div class="item-right">
                <div class="dept-badge">
                  <span class="badge-text">{{ item.sws }}</span>
                </div>
                <div class="action-arrow">
                  <wd-icon name="arrow-right" size="14px" color="#c5d2d9" />
                </div>
              </div>
            </div>
          </div>
        </template>

        <div v-if="recordList.length === 0" class="empty-state">
          <div class="empty-icon">
            <wd-icon name="search" size="48px" color="#c5d2d9" />
          </div>
          <p class="empty-text">未找到相关小区</p>
          <p class="empty-hint">请尝试调整搜索条件</p>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <wd-pagination v-model="params.page" :total="total" :pageSize="params.pageSize" @change="handlePaginationChange" show-icon custom-class="modern-pagination" />
    </div>
  </div>
  <wd-toast />
  <wd-message-box />
</template>

<script setup>
import { reactive, ref, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { zoneRecordList } from '@/services/model/zone.record'
import * as CommonApi from '@/services/model/common'
import { useMessage, useToast } from 'wot-design-uni'

const toast = useToast()
const total = ref(0)
const columns = ref(['全部', '梅林', '福东', '福中', '香蜜'])
const existColumns = ref(['全部', '是', '否'])
const message = useMessage()
const params = reactive({ page: 1, sws: '全部', xqmc: '', pageSize: 15, exist: '' })

onShow(async () => {
  const { data } = await CommonApi.verifyToken()
  if (data) return getZoneRecordList(params)
  recordList.value = []
  message.confirm({ msg: '该页面必须内部员工可查看是否前往登录？', title: '您还未登录' }).then(() => uni.navigateTo({ url: '/pages/login/index' }))
})
const recordList = ref([])

async function getZoneRecordList(params) {
  const P = { ...params, sws: params.sws == '全部' ? '' : params.sws, exist: params.exist == '全部' ? '' : params.exist }
  try {
    toast.loading('正在加载...')
    const { data, pagination } = await zoneRecordList(P)
    total.value = pagination.total
    recordList.value = data
    toast.close()
  } catch (error) {
    toast.error(error.message)
    toast.close()
  }
}

// 页码改变
function handlePaginationChange({ value }) {
  params.page = value
  getZoneRecordList(params)
}

// 模糊搜索
async function searchKeywordName({ value }) {
  params.page = 1
  params.sws = '全部'
  params.exist = '全部'
  params.xqmc = value
  getZoneRecordList(params)
}

watch(
  () => params.xqmc,
  (value) => {
    if (value === '') {
      params.page = 1
      getZoneRecordList(params)
    }
  }
)

function ItemClick(item) {
  uni.vibrateShort({ type: 'medium' })
  uni.navigateTo({ url: `/pages/zone-record/detail?xqbm=${item.xqbm}` })
}

function handleConfirm({ value }) {
  params.page = 1
  params.sws = value
  params.xqmc = ''
  getZoneRecordList(params)
}

function handleExistConfirm({ value }) {
  params.page = 1
  params.exist = value
  params.xqmc = ''
  getZoneRecordList(params)
}

// 显示选择器方法
function showWaterDeptPicker() {
  // 由于wot-design-uni的picker组件特性，我们需要通过编程方式触发
  // 这里可以使用uni.showActionSheet作为替代方案
  uni.showActionSheet({
    itemList: columns.value,
    success: (res) => {
      const selectedValue = columns.value[res.tapIndex]
      handleConfirm({ value: selectedValue })
    }
  })
}

function showPumpRoomPicker() {
  uni.showActionSheet({
    itemList: existColumns.value,
    success: (res) => {
      const selectedValue = existColumns.value[res.tapIndex]
      handleExistConfirm({ value: selectedValue })
    }
  })
}
</script>

<style lang="less" scoped>
// 主容器
.zone-record-container {
  background: linear-gradient(180deg, #f8fafc 0%, #edf2f7 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

// 顶部搜索和筛选区域
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 20rpx 24rpx 20rpx;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
}

.search-container {
  margin-bottom: 16rpx;
}

.search-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 0 16rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  .search-icon {
    position: absolute;
    left: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
  }
}

// 筛选器和统计区域样式
.filter-stats-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
}

.filter-chips {
  display: flex;
  gap: 12rpx;
  flex: 1;
}

.filter-chip {
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 18rpx;
  padding: 10rpx 16rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10rpx);

  &:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.3);
  }

  .chip-label {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
  }
}

// 紧凑统计样式
.stats-compact {
  background: rgba(255, 255, 255, 0.25);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 18rpx;
  padding: 10rpx 16rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  backdrop-filter: blur(15rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

  .stats-icon {
    width: 32rpx;
    height: 32rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  }

  .stats-text {
    display: flex;
    align-items: baseline;
    gap: 4rpx;

    .stats-number {
      font-size: 28rpx;
      font-weight: 700;
      color: rgba(255, 255, 255, 0.95);
      line-height: 1;
    }

    .stats-label {
      font-size: 20rpx;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 500;
    }
  }
}

// 内容区域
.content-section {
  flex: 1;
  padding: 8rpx 16rpx 0 16rpx;
  overflow: hidden;
  min-height: 0;
}

.zone-list {
  height: 100%;
  overflow-y: auto;
  padding: 0 8rpx 16rpx 8rpx;
  scroll-behavior: smooth;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 4rpx;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.02);
    border-radius: 2rpx;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 2rpx;

    &:hover {
      background: rgba(102, 126, 234, 0.5);
    }
  }
}

// 列表项样式
.zone-item {
  background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03), 0 1rpx 6rpx rgba(0, 0, 0, 0.02);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
  }

  &:active {
    transform: translateY(-2rpx) scale(0.98);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(102, 126, 234, 0.12);

    &::before {
      width: 6rpx;
    }
  }

  &.item-alternate {
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  }
}

.item-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18rpx 20rpx;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.zone-avatar {
  margin-right: 16rpx;
  flex-shrink: 0;

  .avatar-bg {
    width: 48rpx;
    height: 48rpx;
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
    border: 2rpx solid rgba(74, 144, 226, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }
}

.zone-details {
  flex: 1;
  min-width: 0;

  .zone-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 6rpx 0;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .zone-meta {
    display: flex;
    align-items: center;
    gap: 6rpx;
    font-size: 20rpx;
    color: #718096;
    line-height: 1.3;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 4rpx;
    }

    .meta-divider {
      color: #cbd5e0;
      font-weight: bold;
    }
  }
}

.item-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-shrink: 0;
}

.dept-badge {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border: 1rpx solid rgba(102, 126, 234, 0.2);
  border-radius: 10rpx;
  padding: 6rpx 12rpx;

  .badge-text {
    font-size: 20rpx;
    font-weight: 600;
    color: #667eea;
    line-height: 1;
  }
}

.action-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  transition: all 0.3s ease;
}

.zone-item:active {
  .zone-avatar .avatar-bg {
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.2) 0%, rgba(102, 126, 234, 0.2) 100%);
    border-color: rgba(74, 144, 226, 0.4);
    transform: scale(1.05);
  }

  .action-arrow {
    transform: translateX(4rpx);
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;

  .empty-icon {
    margin-bottom: 24rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #4a5568;
    margin: 0 0 8rpx 0;
    line-height: 1.3;
  }

  .empty-hint {
    font-size: 24rpx;
    color: #718096;
    margin: 0;
    line-height: 1.4;
  }
}

// 分页样式
.pagination-section {
  padding: 16rpx 24rpx;
  background: transparent;
  flex-shrink: 0;
}

// 动画效果
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .filter-stats-section {
    flex-direction: column;
    gap: 12rpx;
    align-items: stretch;
  }

  .filter-chips {
    justify-content: center;
  }

  .stats-compact {
    align-self: center;
  }

  .item-main {
    padding: 16rpx 18rpx;
  }

  .zone-title {
    font-size: 26rpx;
  }

  .zone-avatar .avatar-bg {
    width: 44rpx;
    height: 44rpx;
  }

  .header-section {
    padding: 16rpx 16rpx 20rpx 16rpx;
  }
}

// 自定义搜索框样式
:deep(.modern-search) {
  .wd-search__input {
    padding-left: 80rpx !important;
    font-size: 28rpx !important;
    color: #2d3748 !important;

    &::placeholder {
      color: #a0aec0 !important;
    }
  }

  .wd-search__block {
    background: transparent !important;
    border: none !important;
  }
}

// 自定义分页样式
:deep(.modern-pagination) {
  .wd-pagination {
    background: rgba(255, 255, 255, 0.8) !important;
    border-radius: 16rpx !important;
    padding: 16rpx !important;
    backdrop-filter: blur(10rpx) !important;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04) !important;
  }
}
</style>
