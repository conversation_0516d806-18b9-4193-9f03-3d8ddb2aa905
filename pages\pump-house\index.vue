<template>
  <div class="pump-house-container all">
    <!-- 顶部搜索区域 -->
    <div class="header-section">
      <div class="search-container">
        <!-- 主搜索区域 -->
        <div class="search-main">
          <div class="search-input-wrapper">
            <div class="search-icon">
              <wd-icon name="search" size="20px" color="white" />
            </div>
            <wd-search v-model="params.pumpHouseName" hide-cancel placeholder="搜索泵房名称" custom-class="enhanced-search" @search="handleSearch" @change="handleSearchChange" />
            <div class="search-actions">
              <div class="action-btn filter-btn" @click="popupOpen = true" :class="{ active: hasActiveFilters }">
                <wd-icon name="filter" size="18px" />
                <span class="filter-badge" v-if="hasActiveFilters"></span>
              </div>
              <div class="action-btn refresh-btn" @click="handleRefresh">
                <wd-icon name="refresh" size="18px" />
              </div>
            </div>
          </div>
        </div>

        <!-- 快速筛选标签 -->
        <div class="quick-filters" v-if="quickFilterTags.length > 0">
          <div class="filter-tags">
            <div v-for="tag in quickFilterTags" :key="tag.key" class="filter-tag" @click="removeQuickFilter(tag.key)">
              <span class="tag-text">{{ tag.label }}</span>
              <wd-icon name="close" size="12px" />
            </div>
            <div class="clear-all-btn" @click="clearAllFilters">
              <wd-icon name="close-circle" size="14px" />
              <span>清除全部</span>
            </div>
          </div>
        </div>

        <!-- 搜索统计信息 -->
        <div class="search-stats" v-if="PumpHouseList.length > 0 || params.pumpHouseName">
          <div class="stats-content">
            <div class="result-count">
              <wd-icon name="list" size="14px" color="#40a9ff" />
              <span>找到 </span>
              <span class="count-number">{{ total }}</span>
              <span> 个泵房</span>
            </div>
            <div class="search-time" v-if="searchTime">
              <span>用时 {{ searchTime }}ms</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 泵房列表 -->
    <div class="content-section">
      <scroll-view class="scroll-container" scroll-y="true" v-if="PumpHouseList.length > 0">
        <div class="pump-house-list">
          <template v-for="(item, index) in PumpHouseList" :key="index">
            <PumpHouseCard :item="item" :index="index" @click="handleItemClick" />
          </template>
        </div>
      </scroll-view>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <div class="empty-icon">
          <wd-icon name="search" size="48px" color="#ffffff" />
        </div>
        <h3 class="empty-text">暂无泵房数据</h3>
        <p class="empty-hint color-white">{{ params.pumpHouseName ? '未找到相关泵房信息' : '当前没有泵房记录' }}</p>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="total > 0">
      <wd-pagination v-model="params.page" :total="total" :pageSize="params.pageSize" @change="handlePaginationChange" show-icon custom-class="modern-pagination" />
    </div>
  </div>

  <wd-popup v-model="popupOpen" class="wd-popup-wrap" :close-on-click-modal="false" closable position="bottom" custom-style="height: 450px; border-radius: 24rpx 24rpx 0 0;" @close="handleClose">
    <div class="filter-popup">
      <!-- 弹窗头部 -->
      <div class="popup-header">
        <div class="header-title">
          <wd-icon name="filter" size="20px" color="#40a9ff" />
          <span>泵房数据筛选</span>
        </div>
        <div class="header-subtitle">设置筛选条件和压力参数</div>
      </div>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <div class="section-title">
          <wd-icon name="location" size="16px" color="#52c41a" />
          <span>区域筛选</span>
        </div>
        <div class="picker-container">
          <div class="picker-wrapper">
            <wd-picker :columns="columns" label-width="22%" label="筛选项" clearable v-model="value" @confirm="handleConfirm" :column-change="onChangeDistrict" :display-format="displayFormat" custom-class="modern-picker" />
          </div>
          <div class="clear-btn" @click="handlePickerClear">
            <wd-icon name="close-circle" size="18px" color="#ff4d4f" />
            <span>清除</span>
          </div>
        </div>
      </div>

      <!-- 压力参数区域 -->
      <div class="pressure-section">
        <div class="section-title">
          <wd-icon name="setting" size="16px" color="#faad14" />
          <span>末端压力参数</span>
        </div>
        <div class="pressure-controls">
          <div class="pressure-item">
            <div class="zone-label low-zone">
              <wd-icon name="arrow-up" size="12px" />
              <span>低区大于</span>
            </div>
            <div class="input-wrapper">
              <wd-input-number :disabled="!LowZoneTheoreticalEndPressureChecked" v-model="params.LowZoneTheoreticalEndPressure" :min="-50" :precision="2" :step="0.01" custom-class="pressure-input" />
              <span class="unit">MPa</span>
            </div>
            <div class="switch-wrapper">
              <wd-switch v-model="LowZoneTheoreticalEndPressureChecked" @change="(e) => handleSwitch(e, 'LowZoneTheoreticalEndPressure')" :size="18" active-color="#52c41a" />
            </div>
          </div>

          <div class="pressure-item">
            <div class="zone-label mid-zone">
              <wd-icon name="arrow-up" size="12px" />
              <span>中区大于</span>
            </div>
            <div class="input-wrapper">
              <wd-input-number :disabled="!MidZoneTheoreticalEndPressureChecked" v-model="params.MidZoneTheoreticalEndPressure" :min="-50" :precision="2" :step="0.01" custom-class="pressure-input" />
              <span class="unit">MPa</span>
            </div>
            <div class="switch-wrapper">
              <wd-switch v-model="MidZoneTheoreticalEndPressureChecked" @change="(e) => handleSwitch(e, 'MidZoneTheoreticalEndPressure')" :size="18" active-color="#1890ff" />
            </div>
          </div>

          <div class="pressure-item">
            <div class="zone-label high-zone">
              <wd-icon name="arrow-up" size="12px" />
              <span>高区大于</span>
            </div>
            <div class="input-wrapper">
              <wd-input-number :disabled="!HighZoneTheoreticalEndPressureChecked" v-model="params.HighZoneTheoreticalEndPressure" :min="-50" :precision="2" :step="0.01" custom-class="pressure-input" />
              <span class="unit">MPa</span>
            </div>
            <div class="switch-wrapper">
              <wd-switch v-model="HighZoneTheoreticalEndPressureChecked" @change="(e) => handleSwitch(e, 'HighZoneTheoreticalEndPressure')" :size="18" active-color="#faad14" />
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="popup-footer">
        <wd-button type="success" @click="handleApply" custom-class="apply-button" block>
          <wd-icon name="check" size="16px" />
          <span>应用筛选</span>
        </wd-button>
      </div>
    </div>
  </wd-popup>

  <wd-toast />
  <wd-message-box />
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { PumpHouseApi } from '@/services/model/pump.house'
import { verifyToken } from '@/services/model/common.js'
import PumpHouseCard from '@/components/PumpHouseCard/enhanced.vue'

import { useToast, useMessage } from 'wot-design-uni'

const toast = useToast()
const total = ref(0)
const message = useMessage()
const popupOpen = ref(false)
const LowZoneTheoreticalEndPressureChecked = ref(false)
const MidZoneTheoreticalEndPressureChecked = ref(false)
const HighZoneTheoreticalEndPressureChecked = ref(false)

// 新增的响应式数据
const searchTime = ref(0)
const quickFilterTags = ref([])
const hasActiveFilters = ref(false)

const params = reactive({ page: 1, pageSize: 10, all: true, pumpHouseName: '', LowZoneTheoreticalEndPressure: 0.28, MidZoneTheoreticalEndPressure: 0.28, HighZoneTheoreticalEndPressure: 0.28, scope: '' })

onShow(async () => {
  try {
    const { data } = await verifyToken()
    if (data) {
      getPumpHouseList(params)
    } else {
      message.confirm({ msg: '该页面必须内部员工可查看是否前往登录？', title: '您还未登录' }).then(() => uni.navigateTo({ url: '/pages/login/index' }))
    }
  } catch (error) {}
})

const PumpHouseList = ref([])

async function getPumpHouseList(P) {
  try {
    const startTime = Date.now()
    toast.loading('正在加载...')
    const par = { ...P }
    if (!LowZoneTheoreticalEndPressureChecked.value) delete par.LowZoneTheoreticalEndPressure
    if (!MidZoneTheoreticalEndPressureChecked.value) delete par.MidZoneTheoreticalEndPressure
    if (!HighZoneTheoreticalEndPressureChecked.value) delete par.HighZoneTheoreticalEndPressure

    const { data, pagination } = await PumpHouseApi.list(par)
    PumpHouseList.value = data || []
    total.value = pagination?.total || 0

    // 计算搜索时间
    searchTime.value = Date.now() - startTime

    // 更新筛选状态
    updateFilterStatus()

    toast.close()
  } catch (error) {
    console.error('获取泵房列表失败:', error)
    toast.error('获取泵房列表失败')
    PumpHouseList.value = []
    total.value = 0
    searchTime.value = 0
    toast.close()
  }
}

// 搜索处理
function handleSearch({ value }) {
  params.page = 1
  params.pumpHouseName = value
  getPumpHouseList(params)
}

function handleSearchChange({ value }) {
  if (value === '') {
    params.page = 1
    params.pumpHouseName = ''
    getPumpHouseList(params)
  }
}

// 分页处理
function handlePaginationChange({ value }) {
  params.page = value
  getPumpHouseList(params)
}

// 点击列表项
function handleItemClick(item) {
  uni.vibrateShort({ type: 'medium' })
  // 这里可以跳转到详情页面
  uni.navigateTo({ url: `/pages/pump-house/detail?pumpRoomNumber=${item.PumpRoomNumber}` })
}

// 监听搜索关键词变化
watch(
  () => params.pumpHouseName,
  (value) => {
    if (value === '') {
      params.page = 1
      getPumpHouseList(params)
    }
  }
)

// ======================================//====================================
const district = {
  0: [
    { label: '水务所', value: 'subject' },
    { label: '片区', value: 'BelongingArea' },
    { label: '网格', value: 'Gridding' }
  ],
  subject: [
    { label: '梅林水务所', value: '梅林' },
    { label: '福东水务所', value: '福东' },
    { label: '福中水务所', value: '福中' },
    { label: '香蜜水务所', value: '香蜜' }
  ],
  BelongingArea: [
    { label: '梅林片区', value: '梅林片区' },
    { label: '景田片区', value: '景田片区' },
    { label: '香蜜片区', value: '香蜜片区' },
    { label: '福东南片区', value: '福东南片区' },
    { label: '福东北片区', value: '福东北片区' },
    { label: '莲花片区', value: '莲花片区' },
    { label: '中心城片区', value: '中心城片区' },
    { label: '福保片区', value: '福保片区' },
    { label: '福民片区', value: '福民片区' },
    { label: '新洲片区', value: '新洲片区' }
  ],
  Gridding: [
    { label: '梅林-1', value: '梅林-1' },
    { label: '梅林-2', value: '梅林-2' },
    { label: '香蜜-1', value: '香蜜-1' },
    { label: '香蜜-2', value: '香蜜-2' },
    { label: '香蜜-3', value: '香蜜-3' },
    { label: '福中-1', value: '福中-1' },
    { label: '福中-2', value: '福中-2' },
    { label: '福中-3', value: '福中-3' },
    { label: '福东-1', value: '福东-1' },
    { label: '福东-2', value: '福东-2' }
  ]
}

const value = ref([])

const columns = ref([district[0], district[district[0][0].value]])

const onChangeDistrict = (pickerView, value, columnIndex, resolve) => {
  const item = value[columnIndex]
  if (columnIndex === 0) {
    pickerView.setColumnData(1, district[item.value])
    pickerView.setColumnData(2, district[district[item.value][0].value])
  } else if (columnIndex === 1) {
    pickerView.setColumnData(2, district[item.value])
  }
  resolve()
}
const displayFormat = (items) => items.map((item) => item.label).join('-')

function handleConfirm({ value }) {
  params.scope = value.join(':')
}

function handleSwitch(key, { value }) {
  if (value) {
    params[key] = 0.28
  }
}

function handleApply() {
  params.page = 1
  params.pumpHouseName = ''
  getPumpHouseList(params)
  popupOpen.value = false
}

function handleClose(e) {
  console.log(e)
}

function handlePickerClear() {
  value.value = []
  params.scope = ''
}

// 新增的方法
function updateFilterStatus() {
  const tags = []

  // 检查搜索关键词
  if (params.pumpHouseName) {
    tags.push({ key: 'search', label: `搜索: ${params.pumpHouseName}` })
  }

  // 检查区域筛选
  if (params.scope) {
    const scopeParts = params.scope.split(':')
    tags.push({ key: 'scope', label: `区域: ${scopeParts.join('-')}` })
  }

  // 检查压力参数
  if (LowZoneTheoreticalEndPressureChecked.value) {
    tags.push({ key: 'lowPressure', label: `低区压力: >${params.LowZoneTheoreticalEndPressure}MPa` })
  }
  if (MidZoneTheoreticalEndPressureChecked.value) {
    tags.push({ key: 'midPressure', label: `中区压力: >${params.MidZoneTheoreticalEndPressure}MPa` })
  }
  if (HighZoneTheoreticalEndPressureChecked.value) {
    tags.push({ key: 'highPressure', label: `高区压力: >${params.HighZoneTheoreticalEndPressure}MPa` })
  }

  quickFilterTags.value = tags
  hasActiveFilters.value = tags.length > 0
}

function removeQuickFilter(key) {
  switch (key) {
    case 'search':
      params.pumpHouseName = ''
      break
    case 'scope':
      params.scope = ''
      value.value = []
      break
    case 'lowPressure':
      LowZoneTheoreticalEndPressureChecked.value = false
      break
    case 'midPressure':
      MidZoneTheoreticalEndPressureChecked.value = false
      break
    case 'highPressure':
      HighZoneTheoreticalEndPressureChecked.value = false
      break
  }
  params.page = 1
  getPumpHouseList(params)
}

function clearAllFilters() {
  params.pumpHouseName = ''
  value.value = []
  params.scope = ''
  LowZoneTheoreticalEndPressureChecked.value = false
  MidZoneTheoreticalEndPressureChecked.value = false
  HighZoneTheoreticalEndPressureChecked.value = false
  params.page = 1
  getPumpHouseList(params)
}

function handleRefresh() {
  uni.vibrateShort({ type: 'light' })
  getPumpHouseList(params)
}
</script>

<style lang="less" scoped>
.pump-house-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

// 顶部搜索区域样式
.header-section {
  padding: 24rpx 24rpx 16rpx;
  background: transparent;
  position: sticky;
  top: 0;
  z-index: 10;
  backdrop-filter: blur(10rpx);
}

.search-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.search-main {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 8rpx 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  /* backdrop-filter: blur(20rpx); 小程序不支持 */
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12rpx;
  position: relative;
}

.search-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44rpx;
  height: 44rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  color: #64748b;

  &.active {
    background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
    color: rgb(97, 167, 7);
    border-color: transparent;
    box-shadow: 0 4rpx 12rpx rgba(64, 169, 255, 0.3);
  }
}

.filter-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 12rpx;
  height: 12rpx;
  background: #ff4d4f;
  border-radius: 50%;
  border: 2rpx solid white;
  animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

// 快速筛选标签样式
.quick-filters {
  animation: slideDown 0.3s ease-out;
}

.filter-tags {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-wrap: wrap;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  color: white;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(64, 169, 255, 0.2);
}

.tag-text {
  white-space: nowrap;
}

.clear-all-btn {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 6rpx 12rpx;
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 77, 79, 0.2);
}

// 搜索统计信息样式
.search-stats {
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  /* backdrop-filter: blur(10rpx); 小程序不支持 */
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  animation: slideDown 0.3s ease-out;
}

.stats-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.result-count {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #4a5568;
}

.count-number {
  color: #40a9ff;
  font-weight: 600;
  font-size: 26rpx;
}

.search-time {
  font-size: 20rpx;
  color: #8e9aaf;
  opacity: 0.8;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 内容区域样式
.content-section {
  flex: 1;
  padding: 0 24rpx;
  overflow: hidden;
}

.scroll-container {
  height: 100%;
  width: 100%;
}

.pump-house-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding-bottom: 20rpx;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
  margin: 0 0 8rpx 0;
  line-height: 1.3;
}

.empty-hint {
  font-size: 24rpx;
  color: #fff;
  margin: 0;
  line-height: 1.4;
}

// 分页样式
.pagination-section {
  padding: 16rpx 24rpx;
  background: transparent;
  flex-shrink: 0;
}

// 自定义搜索框样式
:deep(.enhanced-search) {
  flex: 1;

  .wd-search__input {
    padding: 12rpx 16rpx !important;
    font-size: 28rpx !important;
    color: #2d3748 !important;
    background: rgba(248, 250, 252, 0.8) !important;
    border-radius: 12rpx !important;
    border: 1rpx solid rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease !important;
    // height: 44rpx !important;
    line-height: 44rpx !important;

    &::placeholder {
      color: #a0aec0 !important;
    }
  }

  .wd-search__block {
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    height: 44rpx !important;
  }

  .wd-search__content {
    background: transparent !important;
    height: 44rpx !important;
  }

  .wd-search__field {
    height: 44rpx !important;
    line-height: 44rpx !important;
  }
}

/* 搜索框样式 - 小程序兼容写法 */
.enhanced-search .wd-search__input {
  padding: 12rpx 16rpx !important;
  font-size: 28rpx !important;
  color: #2d3748 !important;
  background: rgba(248, 250, 252, 0.8) !important;
  border-radius: 12rpx !important;
  border: 1rpx solid rgba(0, 0, 0, 0.08) !important;
  height: 44rpx !important;
  line-height: 44rpx !important;
}

.enhanced-search .wd-search__block {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  height: 44rpx !important;
}

.enhanced-search .wd-search__content {
  background: transparent !important;
  height: 44rpx !important;
}

.enhanced-search .wd-search__field {
  height: 44rpx !important;
  line-height: 44rpx !important;
}

/* 全局搜索框样式覆盖 */
.wd-search__input {
  padding: 12rpx 16rpx !important;
  font-size: 28rpx !important;
  color: #2d3748 !important;
  background: rgba(248, 250, 252, 0.8) !important;
  border-radius: 12rpx !important;
  border: 1rpx solid rgba(0, 0, 0, 0.08) !important;
  height: 44rpx !important;
  line-height: 44rpx !important;
}

.wd-search__block {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  height: 44rpx !important;
}

// 自定义分页样式
:deep(.modern-pagination) {
  .wd-pagination {
    background: rgba(255, 255, 255, 0.8) !important;
    border-radius: 16rpx !important;
    padding: 16rpx !important;
    /* backdrop-filter: blur(10rpx) !important; 小程序不支持 */
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04) !important;
  }
}

/* 分页样式 - 小程序兼容写法 */
.modern-pagination .wd-pagination {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 16rpx !important;
  padding: 16rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04) !important;
}

/* 全局分页样式覆盖 */
.wd-pagination {
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 16rpx !important;
  padding: 16rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04) !important;
}

// 动画效果
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.pump-house-item:hover {
  animation: pulse 2s infinite;
}

// 筛选弹窗样式
.filter-popup {
  padding: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}

.popup-header {
  padding: 32rpx 32rpx 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    background: rgba(255, 255, 255, 0.2);
  }
}
/* 弹窗关闭按钮样式 - 小程序兼容写法 */
.wd-popup-wrap .wd-popup__close {
  color: #ffffff !important;
}

/* 更强的选择器优先级 */
.filter-popup .wd-popup__close {
  color: #ffffff !important;
}

/* 全局样式覆盖 */
.wd-popup__close {
  color: #ffffff !important;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
  line-height: 1.4;
}

.filter-section,
.pressure-section {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.picker-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.picker-wrapper {
  flex: 1;
  background: white;
  border-radius: 12rpx;
  padding: 8rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  :deep(.wd-picker__cell) {
    padding: 0 !important;
  }

  /* 选择器样式 - 小程序兼容写法 */
  .wd-picker__cell {
    padding: 0 !important;
  }
}

.clear-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 77, 79, 0.2);

  &:active {
    background: rgba(255, 77, 79, 0.15);
    transform: scale(0.95);
  }
}

.pressure-controls {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.pressure-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  }
}

.zone-label {
  display: flex;
  align-items: center;
  gap: 6rpx;
  min-width: 80rpx;
  font-size: 24rpx;
  font-weight: 600;

  &.low-zone {
    color: #52c41a;
  }

  &.mid-zone {
    color: #1890ff;
  }

  &.high-zone {
    color: #faad14;
  }
}

.input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8rpx;
  position: relative;
}

.unit {
  font-size: 22rpx;
  color: #8c8c8c;
  font-weight: 500;
}

.switch-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60rpx;
}

.popup-footer {
  padding: 24rpx 32rpx 32rpx;
  background: white;
}

// 自定义组件样式
:deep(.modern-picker) {
  .wd-picker__field {
    background: transparent !important;
    border: none !important;
    font-size: 26rpx !important;
    color: #2d3748 !important;
  }
}

:deep(.pressure-input) {
  .wd-input-number {
    background: rgba(248, 250, 252, 0.8) !important;
    border: 1rpx solid rgba(0, 0, 0, 0.08) !important;
    border-radius: 8rpx !important;
    font-size: 24rpx !important;
  }

  .wd-input-number__input {
    color: #2d3748 !important;
    text-align: center !important;
  }
}

:deep(.apply-button) {
  .wd-button {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%) !important;
    border: none !important;
    border-radius: 16rpx !important;
    font-size: 28rpx !important;
    font-weight: 600 !important;
    padding: 20rpx !important;
    box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.3) !important;
    transition: all 0.3s ease !important;

    &:active {
      transform: translateY(2rpx) !important;
      box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.2) !important;
    }
  }

  .wd-button__content {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8rpx !important;
  }
}

/* 组件样式 - 小程序兼容写法 */
.modern-picker .wd-picker__field {
  background: transparent !important;
  border: none !important;
  font-size: 26rpx !important;
  color: #2d3748 !important;
}

.pressure-input .wd-input-number {
  background: rgba(248, 250, 252, 0.8) !important;
  border: 1rpx solid rgba(0, 0, 0, 0.08) !important;
  border-radius: 8rpx !important;
  font-size: 24rpx !important;
}

.pressure-input .wd-input-number__input {
  color: #2d3748 !important;
  text-align: center !important;
}

.apply-button .wd-button {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%) !important;
  border: none !important;
  border-radius: 16rpx !important;
  font-size: 28rpx !important;
  font-weight: 600 !important;
  padding: 20rpx !important;
  box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.3) !important;
}

.apply-button .wd-button__content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8rpx !important;
}

/* 全局组件样式覆盖 */
.wd-picker__field {
  background: transparent !important;
  border: none !important;
  font-size: 26rpx !important;
  color: #2d3748 !important;
}

.wd-input-number {
  background: rgba(248, 250, 252, 0.8) !important;
  border: 1rpx solid rgba(0, 0, 0, 0.08) !important;
  border-radius: 8rpx !important;
  font-size: 24rpx !important;
}

.wd-input-number__input {
  color: #2d3748 !important;
  text-align: center !important;
}

.wd-button {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%) !important;
  border: none !important;
  border-radius: 16rpx !important;
  font-size: 28rpx !important;
  font-weight: 600 !important;
  padding: 20rpx !important;
  box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.3) !important;
}

.wd-button__content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8rpx !important;
}

// 小程序端样式强制覆盖
/* 确保在小程序中样式生效的最终方案 */

/* 弹窗关闭按钮 - 最高优先级 */
.wd-popup .wd-popup__close,
.wd-popup-wrap .wd-popup__close,
.filter-popup .wd-popup__close {
  color: #ffffff !important;
  background: transparent !important;
}

/* 搜索框 - 最高优先级 */
.search-input-wrapper .wd-search .wd-search__input,
.enhanced-search .wd-search__input {
  padding: 12rpx 16rpx !important;
  font-size: 28rpx !important;
  color: #2d3748 !important;
  background: rgba(248, 250, 252, 0.8) !important;
  border-radius: 12rpx !important;
  border: 1rpx solid rgba(0, 0, 0, 0.08) !important;
  height: 44rpx !important;
  line-height: 44rpx !important;
}

.search-input-wrapper .wd-search .wd-search__block,
.enhanced-search .wd-search__block {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  height: 44rpx !important;
}

/* 选择器 - 最高优先级 */
.picker-wrapper .wd-picker .wd-picker__cell {
  padding: 0 !important;
}

/* 输入框 - 最高优先级 */
.pressure-item .wd-input-number {
  background: rgba(248, 250, 252, 0.8) !important;
  border: 1rpx solid rgba(0, 0, 0, 0.08) !important;
  border-radius: 8rpx !important;
  font-size: 24rpx !important;
}

.pressure-item .wd-input-number .wd-input-number__input {
  color: #2d3748 !important;
  text-align: center !important;
}

/* 按钮 - 最高优先级 */
.popup-footer .wd-button {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%) !important;
  border: none !important;
  border-radius: 16rpx !important;
  font-size: 28rpx !important;
  font-weight: 600 !important;
  padding: 20rpx !important;
  box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.3) !important;
}

/* 小程序端背景优化 */
.search-main {
  background: rgba(255, 255, 255, 0.98) !important;
}

.filter-tags {
  background: rgba(255, 255, 255, 0.98) !important;
}

.search-stats {
  background: rgba(255, 255, 255, 0.98) !important;
}

.action-btn {
  background: rgba(248, 250, 252, 0.95) !important;
}

// 响应式设计
@media (max-width: 750rpx) {
  .search-input-wrapper {
    gap: 8rpx;
  }

  .action-btn {
    width: 40rpx;
    height: 40rpx;
  }

  .filter-tag {
    font-size: 20rpx;
    padding: 4rpx 8rpx;
  }

  .clear-all-btn {
    font-size: 20rpx;
    padding: 4rpx 8rpx;
  }

  .filter-popup {
    .popup-header {
      padding: 24rpx 24rpx 20rpx;
    }

    .filter-section,
    .pressure-section {
      padding: 20rpx 24rpx;
    }

    .popup-footer {
      padding: 20rpx 24rpx 24rpx;
    }

    .pressure-item {
      padding: 12rpx;
      gap: 12rpx;
    }

    .zone-label {
      min-width: 60rpx;
      font-size: 22rpx;
    }
  }
}
</style>
