# 微信小程序 Textarea 键盘自动收起问题解决方案

## 问题描述

在微信小程序中，iOS 用户在使用 textarea 输入内容时，键盘会自动收起，而 Android 用户不会出现这个问题。

## 问题根本原因

经过深入分析，发现问题的根本原因是 **ActionSheet 组件的触摸事件处理机制**：

1. **触摸事件冲突**：ActionSheet 在根元素上监听了 `touchstart`、`touchmove`、`touchend` 事件
2. **事件阻止**：`@touchmove.stop.prevent` 阻止了事件冒泡和默认行为
3. **iOS 特殊性**：在 iOS 上，这些触摸事件处理会干扰 textarea 的焦点管理，导致键盘自动收起

## 解决方案

### 1. 修改 ActionSheet 组件

在 `components/ActionSheet/index.vue` 中进行以下修改：

#### 1.1 修改触摸开始事件处理

```javascript
function handleTouchStart(event) {
  if (!props.isDragging) return
  
  // 检查是否点击的是 textarea 或 input 元素，如果是则不处理拖拽
  const target = event.target || event.currentTarget
  if (target && (target.tagName === 'TEXTAREA' || target.tagName === 'INPUT' || 
      target.classList.contains('uni-textarea-textarea') || 
      target.classList.contains('uni-input-input'))) {
    return
  }
  
  touch.touchStart(event)
  dragging.value = true
  startY = -height.value
}
```

#### 1.2 修改触摸移动事件处理

```javascript
function handleTouchMove(event) {
  if (!props.isDragging) return
  
  // 检查是否在 textarea 或 input 元素上移动，如果是则不处理拖拽
  const target = event.target || event.currentTarget
  if (target && (target.tagName === 'TEXTAREA' || target.tagName === 'INPUT' || 
      target.classList.contains('uni-textarea-textarea') || 
      target.classList.contains('uni-input-input'))) {
    return
  }

  touch.touchMove(event)
  const moveY = touch.deltaY.value + startY
  const min = anchorHeight.value[0]
  const max = anchorHeight.value[anchorHeight.value.length - 1]
  if (-moveY < min || -moveY > max) return
  updateHeight(-moveY)
}
```

#### 1.3 添加 scroll-view 专用事件处理

```javascript
function handleScrollTouchMove(event) {
  // 检查是否在 textarea 或 input 元素上，如果是则不阻止事件
  const target = event.target || event.currentTarget
  if (target && (target.tagName === 'TEXTAREA' || target.tagName === 'INPUT' || 
      target.classList.contains('uni-textarea-textarea') || 
      target.classList.contains('uni-input-input'))) {
    return // 不阻止 textarea/input 的默认行为
  }
  
  // 对于其他元素，调用原来的处理逻辑
  event.stopPropagation()
  event.preventDefault()
  handleTouchMove(event)
}
```

#### 1.4 修改模板中的事件绑定

```vue
<scroll-view @touchmove="handleScrollTouchMove" class="content" :scroll-y="scrollY">
  <slot></slot>
</scroll-view>
```

### 2. 可选：为 textarea 添加额外属性

如果需要进一步增强稳定性，可以为 textarea 添加以下属性：

```vue
<textarea 
  :cursor-spacing="30" 
  v-model="inputValue" 
  auto-height 
  placeholder="请输入内容"
  :hold-keyboard="true"
  :adjust-position="true"
  :confirm-hold="true"
/>
```

## 核心解决思路

1. **识别输入元素**：通过检查事件目标的标签名和类名来识别 textarea 和 input 元素
2. **选择性事件处理**：只对非输入元素执行 ActionSheet 的拖拽逻辑
3. **保护输入行为**：让 textarea 和 input 的默认触摸行为不被干扰

## 测试建议

1. 在 iOS 设备上测试 textarea 输入功能
2. 验证点击 textarea 时键盘正常弹起且不会自动收起
3. 确认 ActionSheet 的拖拽功能在非输入区域正常工作
4. 测试 Android 设备上的功能正常

## 注意事项

- 该解决方案针对的是 ActionSheet 组件与输入组件的事件冲突问题
- 修改后的代码保持了 ActionSheet 的原有功能，只是在处理输入元素时更加智能
- 建议在真机上进行测试，特别是 iOS 设备

## 已修复的文件

- `components/ActionSheet/index.vue` - 修复了触摸事件与输入组件的冲突
