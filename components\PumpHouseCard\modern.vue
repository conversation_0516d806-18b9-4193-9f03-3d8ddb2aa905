<template>
  <div 
    @click="handleClick" 
    class="pump-house-card" 
    :class="{ 'card-alternate': index % 2 === 1 }"
  >
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="header-left">
        <div class="pump-house-avatar">
          <div class="avatar-container" :class="getStatusClass(item.status)">
            <div class="avatar-icon">
              <wd-icon name="home" size="24px" color="#ffffff" />
            </div>
            <div class="status-badge" :class="getStatusClass(item.status)">
              <div class="status-pulse"></div>
            </div>
          </div>
        </div>
        <div class="pump-house-info">
          <h3 class="pump-house-title">{{ item.PumpHouseName || '未命名泵房' }}</h3>
          <div class="pump-house-subtitle">
            <span class="room-number">{{ item.PumpRoomNumber || '无编号' }}</span>
            <span class="status-text" :class="getStatusClass(item.status)">
              {{ getStatusText(item.status) }}
            </span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <div class="action-button">
          <wd-icon name="arrow-right" size="18px" color="#8e9aaf" />
        </div>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <div class="info-grid">
        <div class="info-item" v-if="item.ProgressStatus">
          <div class="info-label">
            <wd-icon name="setting" size="14px" color="#40a9ff" />
            <span>进度状态</span>
          </div>
          <div class="info-value">{{ item.ProgressStatus }}</div>
        </div>
        <div class="info-item" v-if="item.zoneCode">
          <div class="info-label">
            <wd-icon name="location" size="14px" color="#52c41a" />
            <span>区域代码</span>
          </div>
          <div class="info-value">{{ item.zoneCode }}</div>
        </div>
      </div>
      
      <div class="address-section" v-if="item.address">
        <div class="address-label">
          <wd-icon name="map-pin" size="12px" color="#8e9aaf" />
          <span>地址</span>
        </div>
        <div class="address-text">{{ item.address }}</div>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="footer-content">
        <div class="action-hint">
          <wd-icon name="eye" size="14px" color="#40a9ff" />
          <span>点击查看详情</span>
        </div>
        <div class="update-time" v-if="item.updateTime">
          <span>{{ formatTime(item.updateTime) }}</span>
        </div>
      </div>
    </div>

    <!-- 点击波纹效果 -->
    <div class="ripple-effect"></div>
  </div>
</template>

<script setup>
const props = defineProps({
  // 泵房数据
  item: {
    type: Object,
    required: true,
    default: () => ({})
  },
  // 列表索引
  index: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['click'])

// 获取状态样式类
function getStatusClass(status) {
  const statusMap = {
    正常: 'status-normal',
    运行: 'status-running',
    维护: 'status-maintenance',
    故障: 'status-error',
    停用: 'status-disabled'
  }
  return statusMap[status] || 'status-unknown'
}

// 获取状态文本
function getStatusText(status) {
  const statusTextMap = {
    正常: '正常运行',
    运行: '运行中',
    维护: '维护中',
    故障: '故障',
    停用: '已停用'
  }
  return statusTextMap[status] || '状态未知'
}

// 格式化时间
function formatTime(time) {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 处理点击事件
function handleClick() {
  emit('click', props.item)
}
</script>

<style lang="less" scoped>
// 现代化卡片样式
.pump-house-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 
    0 8rpx 32rpx rgba(0, 0, 0, 0.06),
    0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  cursor: pointer;

  &:hover {
    transform: translateY(-8rpx) scale(1.02);
    box-shadow: 
      0 20rpx 60rpx rgba(0, 0, 0, 0.12),
      0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  }

  &:active {
    transform: translateY(-4rpx) scale(1.01);
  }

  &.card-alternate {
    background: linear-gradient(135deg, rgba(240, 248, 255, 0.95) 0%, rgba(230, 244, 255, 0.95) 100%);
  }
}

// 卡片头部
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  flex: 1;
}

// 现代化头像设计
.pump-house-avatar {
  position: relative;
}

.avatar-container {
  position: relative;
  width: 88rpx;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  
  &.status-normal {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
  }
  
  &.status-running {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3);
  }
  
  &.status-maintenance {
    background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
    box-shadow: 0 8rpx 24rpx rgba(250, 173, 20, 0.3);
  }
  
  &.status-error {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    box-shadow: 0 8rpx 24rpx rgba(255, 77, 79, 0.3);
  }
}

.avatar-icon {
  z-index: 2;
}

.status-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  border: 3rpx solid white;
  
  &.status-normal {
    background: #52c41a;
  }
  
  &.status-running {
    background: #1890ff;
  }
  
  &.status-maintenance {
    background: #faad14;
  }
  
  &.status-error {
    background: #ff4d4f;
  }
}

.status-pulse {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: inherit;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 泵房信息
.pump-house-info {
  flex: 1;
  min-width: 0;
}

.pump-house-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 12rpx 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pump-house-subtitle {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.room-number {
  font-size: 24rpx;
  color: #64748b;
  background: rgba(100, 116, 139, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.status-text {
  font-size: 22rpx;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  
  &.status-normal {
    color: #52c41a;
    background: rgba(82, 196, 26, 0.1);
  }
  
  &.status-running {
    color: #1890ff;
    background: rgba(24, 144, 255, 0.1);
  }
  
  &.status-maintenance {
    color: #faad14;
    background: rgba(250, 173, 20, 0.1);
  }
  
  &.status-error {
    color: #ff4d4f;
    background: rgba(255, 77, 79, 0.1);
  }
}

.header-right {
  display: flex;
  align-items: center;
}

.action-button {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: rgba(142, 154, 175, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(64, 169, 255, 0.1);
    transform: translateX(4rpx);
  }
}

// 卡片内容
.card-content {
  margin-bottom: 24rpx;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.info-item {
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16rpx;
  padding: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 20rpx;
  color: #8e9aaf;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.info-value {
  font-size: 24rpx;
  color: #2d3748;
  font-weight: 600;
}

.address-section {
  background: rgba(64, 169, 255, 0.05);
  border-radius: 16rpx;
  padding: 16rpx;
  border: 1rpx solid rgba(64, 169, 255, 0.1);
}

.address-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 20rpx;
  color: #8e9aaf;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.address-text {
  font-size: 24rpx;
  color: #2d3748;
  line-height: 1.4;
}

// 卡片底部
.card-footer {
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  padding-top: 20rpx;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-hint {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #40a9ff;
  font-weight: 500;
}

.update-time {
  font-size: 20rpx;
  color: #8e9aaf;
}

// 波纹效果
.ripple-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(64, 169, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transform: scale(0);
  transition: all 0.4s ease;
  pointer-events: none;
  border-radius: 24rpx;
}

.pump-house-card:active .ripple-effect {
  opacity: 1;
  transform: scale(1);
}
</style>
