<template>
  <div class="popup-container">
    <!-- 弹窗标题 -->
    <div class="popup-header">
      <div class="header-icon">📊</div>
      <div class="header-text">
        <h3>详细数据统计</h3>
        <p>排水管渠分类数据详情</p>
      </div>
    </div>

    <!-- 数据范围标识 -->
    <div class="data-scope" v-if="tableTitle">
      <div class="scope-icon">🎯</div>
      <div class="scope-content">
        <div class="scope-label">数据范围</div>
        <div class="scope-title">{{ tableTitle }}</div>
      </div>
    </div>

    <!-- 表格数据 -->
    <div class="popup-content" v-if="popupData">
      <div class="popup-table">
        <!-- 表格头部 -->
        <div class="flex fon-S20 table-header">
          <div class="header-cell type-header">
            <span class="header-icon">📋</span>
            <span class="header-label">类型</span>
          </div>
          <div class="header-cell municipal-header">
            <span class="header-text">市政</span>
            <span class="header-unit">km</span>
          </div>
          <div class="header-cell plot-header">
            <span class="header-text">小区</span>
            <span class="header-unit">km</span>
          </div>
          <div class="header-cell total-header">
            <span class="header-text">合计</span>
            <span class="header-unit">km</span>
          </div>
        </div>

        <!-- 表格数据行 -->
        <template v-for="(item, index) in getAllSubtypes(popupData)" :key="index">
          <div
            class="flex fon-S20 table-row"
            v-if="item.data.plot > 0 || item.data.municipal > 0"
            :class="{
              'row-even': index % 2 === 0,
              'total-row': item.isTotal
            }"
          >
            <div class="data-cell type-cell">
              <span class="type-icon">{{ getTypeIcon(item.subtype) }}</span>
              <span class="type-name">{{ item.subtype }}</span>
            </div>
            <div class="data-cell value-cell municipal-cell">
              <span class="value-number">{{ item.data.municipal.toFixed(2) }}</span>
            </div>
            <div class="data-cell value-cell plot-cell">
              <span class="value-number">{{ item.data.plot.toFixed(2) }}</span>
            </div>
            <div class="data-cell total-cell">
              <span class="total-number">{{ (item.data.plot + item.data.municipal).toFixed(2) }}</span>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 空数据提示 -->
    <div class="empty-data" v-if="!popupData || popupData.length === 0">
      <div class="empty-icon">📋</div>
      <div class="empty-text">暂无数据</div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  popupData: {
    type: Array,
    default: () => []
  },
  tableTitle: {
    type: String,
    default: ''
  }
})

// 获取子类型图标
const getTypeIcon = (subtype) => {
  const iconMap = {
    污水管: '💧',
    雨水管: '🌧️',
    污水渠: '🌊',
    雨水渠: '💦',
    截流管: '🔧',
    截流渠: '⚡',
    排水管: '🔧',
    排水渠: '🌊',
    合计: '📊'
  }
  return iconMap[subtype] || '📊'
}

// 获取所有子类型数据（合并管和渠）
const getAllSubtypes = (popupData) => {
  if (!popupData || popupData.length === 0) return []

  const allSubtypes = []

  // 定义数据分组和合计
  const dataGroups = {
    sewage: { municipal: 0, plot: 0, types: ['污水渠', '污水管'] },
    rainwater: { municipal: 0, plot: 0, types: ['雨水渠', '雨水管'] },
    storage: { municipal: 0, plot: 0, types: ['截流渠', '截流管'] }
  }

  // 定义排序优先级：污水渠优先，然后污水管，再雨水渠、雨水管，最后截流渠、截流管
  const typeOrder = {
    污水渠: 1,
    污水管: 2,
    雨水渠: 3,
    雨水管: 4,
    截流渠: 5,
    截流管: 6
  }

  // 遍历所有分类（排水渠、排水管）
  popupData.forEach((category) => {
    if (category.data && category.data.length > 0) {
      category.data.forEach((subtype) => {
        allSubtypes.push(subtype)

        // 累计污水管和污水渠的数据
        if (subtype.subtype === '污水管' || subtype.subtype === '污水渠') {
          dataGroups.sewage.municipal += subtype.data.municipal || 0
          dataGroups.sewage.plot += subtype.data.plot || 0
        }

        // 累计雨水管和雨水渠的数据
        if (subtype.subtype === '雨水管' || subtype.subtype === '雨水渠') {
          dataGroups.rainwater.municipal += subtype.data.municipal || 0
          dataGroups.rainwater.plot += subtype.data.plot || 0
        }

        // 累计截流管和截流渠的数据
        if (subtype.subtype === '截流管' || subtype.subtype === '截流渠') {
          dataGroups.storage.municipal += subtype.data.municipal || 0
          dataGroups.storage.plot += subtype.data.plot || 0
        }
      })
    }
  })

  // 按照污水渠、污水管的顺序排序
  allSubtypes.sort((a, b) => {
    const orderA = typeOrder[a.subtype] || 999
    const orderB = typeOrder[b.subtype] || 999
    return orderA - orderB
  })

  // 插入合计行的逻辑 - 从后往前插入，避免索引偏移问题

  // 3. 先插入截流管渠合计（从后往前）
  const storagePipeIndex = allSubtypes.findIndex((item) => item.subtype === '截流管')
  if (storagePipeIndex !== -1) {
    allSubtypes.splice(storagePipeIndex + 1, 0, {
      subtype: '合计',
      data: {
        municipal: dataGroups.storage.municipal,
        plot: dataGroups.storage.plot
      },
      isTotal: true
    })
  }

  // 2. 再插入雨水管渠合计
  const rainwaterPipeIndex = allSubtypes.findIndex((item) => item.subtype === '雨水管')
  if (rainwaterPipeIndex !== -1) {
    allSubtypes.splice(rainwaterPipeIndex + 1, 0, {
      subtype: '合计',
      data: {
        municipal: dataGroups.rainwater.municipal,
        plot: dataGroups.rainwater.plot
      },
      isTotal: true
    })
  }

  // 1. 最后插入污水管渠合计
  const sewagePipeIndex = allSubtypes.findIndex((item) => item.subtype === '污水管')
  if (sewagePipeIndex !== -1) {
    allSubtypes.splice(sewagePipeIndex + 1, 0, {
      subtype: '合计',
      data: {
        municipal: dataGroups.sewage.municipal,
        plot: dataGroups.sewage.plot
      },
      isTotal: true
    })
  }

  return allSubtypes
}
</script>

<style lang="less" scoped>
// 弹窗样式
.popup-container {
  padding: 32rpx;
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  height: 100%;
  overflow-y: auto;
}

// 数据范围标识
.data-scope {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  margin-bottom: 24rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
  border-radius: 12rpx;
  border: 1rpx solid #d4edda;
  box-shadow: 0 2rpx 8rpx rgba(40, 167, 69, 0.1);

  .scope-icon {
    font-size: 32rpx;
    margin-right: 16rpx;
    filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
  }

  .scope-content {
    flex: 1;

    .scope-label {
      font-size: 20rpx;
      color: #6c757d;
      margin-bottom: 4rpx;
      font-weight: 500;
    }

    .scope-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #28a745;
      line-height: 1.2;
    }
  }
}

.popup-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);

  .header-icon {
    font-size: 48rpx;
    margin-right: 20rpx;
    filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
  }

  .header-text {
    flex: 1;

    h3 {
      font-size: 36rpx;
      font-weight: 700;
      margin: 0 0 8rpx 0;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }

    p {
      font-size: 24rpx;
      margin: 0;
      opacity: 0.9;
      font-weight: 400;
    }
  }
}

.popup-content {
  .popup-table {
    background: white;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid #e8f4fd;

    .table-header {
      background: linear-gradient(135deg, #f0f8ff 0%, #e8f4fd 100%);
      border-bottom: 2rpx solid #d6ebf5;
      min-height: 80rpx;

      .header-cell {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20rpx 16rpx;
        font-weight: 600;
        font-size: 24rpx;
        border-right: 1rpx solid #d6ebf5;
        gap: 8rpx;
        transition: all 0.3s ease;

        &:last-child {
          border-right: none;
        }

        .header-icon {
          font-size: 20rpx;
          filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
        }

        .header-label,
        .header-text {
          font-weight: 600;
          line-height: 1.2;
        }

        .header-unit {
          font-size: 18rpx;
          opacity: 0.8;
          font-weight: 500;
          background: rgba(255, 255, 255, 0.6);
          padding: 2rpx 6rpx;
          border-radius: 6rpx;
        }

        &.type-header {
          width: 140rpx;
          flex: none;
          background: linear-gradient(135deg, #49a2de 0%, rgba(73, 162, 222, 0.8) 100%);
          color: white;
          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
          align-items: center;
          gap: 6rpx;
        }

        &.municipal-header {
          background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
          color: #1976d2;
        }

        &.plot-header {
          background: linear-gradient(135deg, #fce4ec 0%, #fff0f5 100%);
          color: #6c80e8;
        }

        &.total-header {
          background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
          color: #388e3c;
        }
      }
    }

    .table-row {
      border-bottom: 1rpx solid #f0f8ff;
      min-height: 60rpx;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: linear-gradient(135deg, #f8fbff 0%, #f0f8ff 100%);
        transform: translateY(-1rpx);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      }

      &.row-even {
        background: linear-gradient(135deg, #fafbff 0%, #f8fbff 100%);
      }

      &.total-row {
        background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
        border-top: 2rpx solid #ffa726;
        border-bottom: 2rpx solid #ffa726;
        font-weight: 700;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 6rpx;
          background: linear-gradient(135deg, #ff9800 0%, #ffa726 100%);
        }

        .data-cell {
          font-weight: 700;
          color: #e65100;

          &.type-cell {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            color: #bf360c;
            border-right: 1rpx solid #ffcc02;

            .type-name {
              font-weight: 700;
              font-size: 26rpx;
            }
          }

          &.value-cell {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);

            .value-number {
              font-weight: 700;
              font-size: 28rpx;
              color: #e65100;
            }
          }

          &.total-cell {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);

            .total-number {
              font-weight: 800;
              font-size: 30rpx;
              color: #bf360c;
            }
          }
        }

        &:hover {
          background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
          transform: translateY(-2rpx);
          box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.2);
        }
      }

      .data-cell {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16rpx 12rpx;
        border-right: 1rpx solid #f0f8ff;
        transition: all 0.3s ease;

        &:last-child {
          border-right: none;
        }

        &.type-cell {
          width: 140rpx;
          flex: none;
          background: linear-gradient(135deg, #f8fbff 0%, #f0f8ff 100%);
          font-weight: 600;
          color: #34495e;
          border-right: 1rpx solid #e8f4fd;
          gap: 8rpx;

          .type-icon {
            font-size: 20rpx;
            filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
          }

          .type-name {
            font-size: 24rpx;
            font-weight: 600;
            line-height: 1.2;
          }
        }

        &.value-cell {
          .value-number {
            font-size: 26rpx;
            font-weight: 600;
            font-family: 'Courier New', monospace;
          }

          &.municipal-cell {
            background: linear-gradient(135deg, #f3f8ff 0%, #e8f4fd 100%);
            color: #1976d2;
          }

          &.plot-cell {
            background: linear-gradient(135deg, #fff0f5 0%, #fce4ec 100%);
            color: #6c80e8;
          }
        }

        &.total-cell {
          background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
          font-weight: 600;
          color: #27ae60;
          position: relative;
          gap: 8rpx;

          .total-number {
            font-size: 28rpx;
            font-weight: 700;
            font-family: 'Courier New', monospace;
          }

          .total-badge {
            font-size: 18rpx;
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            padding: 2rpx 6rpx;
            border-radius: 8rpx;
            font-weight: 500;
          }

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 20%;
            bottom: 20%;
            width: 3rpx;
            background: #27ae60;
            border-radius: 0 2rpx 2rpx 0;
          }
        }
      }
    }
  }
}

// 空数据提示
.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;
  color: #95a5a6;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 16rpx;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 28rpx;
    font-weight: 500;
  }
}
</style>
