import { nodeRequest } from '../index.js'

// 字典接口
export const dictionaryLookup = (type) => {
  return nodeRequest.get(`/nodeServer/dictionaries/${type}`)
}

// token有效验证接口
export const verifyToken = () => {
  return nodeRequest.get(`/nodeServer/common/verify`)
}

// 坐标转换接口
export const coordinateTransformation = (data) => {
  return nodeRequest.post('/nodeServer/resource/coordinateTransformation', data)
}
