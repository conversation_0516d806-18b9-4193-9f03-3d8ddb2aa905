<template>
  <div class="mine-page">
    <!-- 状态栏占位 -->
    <div :style="{ height: statusBarHeight + 'px' }"></div>

    <!-- 头部背景区域 -->
    <div class="header-section">
      <div class="header-background">
        <div class="gradient-overlay"></div>
        <div class="mesh-pattern"></div>
        <div class="decoration-circles">
          <div class="circle circle-1"></div>
          <div class="circle circle-2"></div>
          <div class="circle circle-3"></div>
          <div class="circle circle-4"></div>
          <div class="circle circle-5"></div>
        </div>
        <div class="light-rays">
          <div class="ray ray-1"></div>
          <div class="ray ray-2"></div>
          <div class="ray ray-3"></div>
        </div>
      </div>

      <!-- 用户信息卡片 -->
      <div class="user-card">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="user-avatar-container">
            <div class="avatar-glow"></div>
            <div class="avatar-wrapper">
              <div class="avatar" :style="{ backgroundImage: avatar }"></div>
              <div class="avatar-ring"></div>
              <div class="avatar-inner-ring"></div>
              <div class="online-indicator" v-if="userInfo">
                <div class="pulse-ring"></div>
              </div>
            </div>
          </div>

          <div class="user-info">
            <div class="user-name-section">
              <h2 class="user-name">{{ userInfo?.name ?? '未登录' }}</h2>
              <div class="user-badge" v-if="userInfo?.station">
                <div class="badge-shine"></div>
                <span class="badge-text">{{ userInfo?.station }}</span>
              </div>
            </div>
            <div class="user-meta">
              <div class="login-time">
                <div class="time-icon-wrapper">
                  <wd-icon name="time" size="14px" color="#8B9DC3"></wd-icon>
                </div>
                <span>最近登录：{{ formatDate(new Date()) }}</span>
              </div>
              <div class="user-stats">
                <div class="stat-item">
                  <div class="stat-value">{{ userInfo ? '在线' : '离线' }}</div>
                  <div class="stat-label">状态</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-decoration">
          <div class="decoration-dot dot-1"></div>
          <div class="decoration-dot dot-2"></div>
          <div class="decoration-dot dot-3"></div>
        </div>
      </div>
    </div>

    <!-- 功能菜单区域 -->
    <div class="menu-section">
      <div class="section-header">
        <h3 class="section-title">功能菜单</h3>
        <div class="section-subtitle">快速访问常用功能</div>
      </div>

      <div class="menu-card">
        <div class="menu-card-background"></div>
        <div class="menu-item" @click="navigateTo('/pages/mine/subpage/code/index')">
          <div class="menu-item-background"></div>
          <div class="menu-icon test-icon">
            <div class="icon-glow test-glow"></div>
            <wd-icon name="a-controlplatform" size="20px" color="#FFFFFF"></wd-icon>
          </div>
          <div class="menu-content">
            <div class="menu-title">测试页面</div>
            <div class="menu-desc">开发测试功能</div>
          </div>
          <div class="arrow-wrapper">
            <wd-icon name="arrow-right" size="16px" color="#C1C9D2"></wd-icon>
          </div>
        </div>

        <div class="menu-divider">
          <div class="divider-line"></div>
        </div>

        <div class="menu-item" @click="navigateTo('/pages/mine/subpage/feedback/index')">
          <div class="menu-item-background"></div>
          <div class="menu-icon feedback-icon">
            <div class="icon-glow feedback-glow"></div>
            <wd-icon name="logo-codepen" size="20px" color="#FFFFFF"></wd-icon>
          </div>
          <div class="menu-content">
            <div class="menu-title">帮助与反馈</div>
            <div class="menu-desc">意见建议与问题反馈</div>
          </div>
          <div class="arrow-wrapper">
            <wd-icon name="arrow-right" size="16px" color="#C1C9D2"></wd-icon>
          </div>
        </div>

        <div class="menu-divider">
          <div class="divider-line"></div>
        </div>

        <div class="menu-item logout-item" @click="navigateTo('/pages/zone-record/feedback.record')">
          <div class="menu-item-background"></div>
          <div class="menu-icon record-icon">
            <div class="icon-glow logout-glow"></div>
            <wd-icon name="chat" size="20px" color="#FFFFFF"></wd-icon>
          </div>
          <div class="menu-content">
            <wd-badge :modelValue="count">
              <div class="menu-title pad-R24">档案反馈</div>
            </wd-badge>
            <div class="menu-desc">档案数据错误反馈列表</div>
          </div>
          <div class="arrow-wrapper">
            <wd-icon name="arrow-right" size="16px" color="#C1C9D2"></wd-icon>
          </div>
        </div>
        <div class="menu-divider">
          <div class="divider-line"></div>
        </div>

        <div class="menu-item logout-item" @click="logout">
          <div class="menu-item-background"></div>
          <div class="menu-icon logout-icon">
            <div class="icon-glow logout-glow"></div>
            <wd-icon name="logout" size="20px" color="#FFFFFF"></wd-icon>
          </div>
          <div class="menu-content">
            <div class="menu-title logout-text">退出登录</div>
            <div class="menu-desc">安全退出当前账户</div>
          </div>
          <div class="arrow-wrapper">
            <wd-icon name="arrow-right" size="16px" color="#C1C9D2"></wd-icon>
          </div>
        </div>
      </div>

      <!-- 底部装饰区域 -->
      <div class="bottom-decoration">
        <div class="decoration-wave"></div>
        <div class="floating-elements">
          <div class="floating-dot dot-a"></div>
          <div class="floating-dot dot-b"></div>
          <div class="floating-dot dot-c"></div>
        </div>
      </div>
    </div>
  </div>

  <wd-toast />
  <wd-message-box />
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { cache } from '@/utils/cache.js'
import { verifyToken } from '@/services/model/common.js'
import { useMessage } from 'wot-design-uni'
import { feedbackPendingCountApi } from '@/services/model/feedback.issue'

const message = useMessage()
const userInfo = ref(null)
const statusBarHeight = ref(0)
const count = ref(0)

// 头像计算属性
const avatar = computed(() => {
  const url = userInfo.value?.path ?? '/static/img/bg2.jpg'
  return `url(${url})`
})

// 格式化日期
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}/${month}/${day}`
}

// 导航跳转
const navigateTo = (url) => {
  uni.vibrateShort({ type: 'medium' })
  uni.navigateTo({ url })
}

onLoad(() => {
  statusBarHeight.value = uni.getWindowInfo().statusBarHeight
})

// 获取用户信息
onShow(async () => {
  try {
    const { data } = await verifyToken()
    if (!data) {
      return message
        .confirm({
          msg: '您还未登录',
          title: '前往登录？'
        })
        .then(() => {
          uni.navigateTo({ url: '/pages/login/index' })
        })
    }

    const info = cache.get('userInfo')
    if (info) {
      userInfo.value = info
    }

    const { data: countData } = await feedbackPendingCountApi()
    count.value = countData.count
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
})

// 退出登录
const logout = () => {
  uni.vibrateShort({ type: 'medium' })
  message.confirm({
    msg: '确认退出登录？',
    title: '温馨提示',
    beforeConfirm: ({ resolve }) => {
      try {
        cache.clear()
        uni.vibrateShort({ type: 'medium' })
        resolve(true)
        uni.reLaunch({ url: '/pages/login/index' })
      } catch (error) {
        console.error('退出登录失败:', error)
        resolve(false)
      }
    }
  })
}
</script>

<style lang="less" scoped>
.mine-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 头部区域 */
.header-section {
  position: relative;
  padding-bottom: 60rpx;
}

.header-background {
  position: relative;
  height: 400rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.9) 100%);
}

/* 网格纹理 */
.mesh-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: meshMove 20s linear infinite;
}

@keyframes meshMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(20px, 20px);
  }
}

/* 光线效果 */
.light-rays {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.ray {
  position: absolute;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform-origin: center;
}

.ray-1 {
  width: 2px;
  height: 200rpx;
  top: 50rpx;
  left: 20%;
  animation: rayRotate 8s linear infinite;
}

.ray-2 {
  width: 1px;
  height: 150rpx;
  top: 100rpx;
  right: 30%;
  animation: rayRotate 12s linear infinite reverse;
}

.ray-3 {
  width: 3px;
  height: 180rpx;
  bottom: 80rpx;
  left: 60%;
  animation: rayRotate 10s linear infinite;
}

@keyframes rayRotate {
  0% {
    transform: rotate(0deg) translateY(-10px);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: rotate(360deg) translateY(-10px);
    opacity: 0;
  }
}

/* 装饰圆圈 */
.decoration-circles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -50rpx;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 120rpx;
  height: 120rpx;
  top: 100rpx;
  left: -30rpx;
  animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
  width: 80rpx;
  height: 80rpx;
  bottom: 50rpx;
  right: 100rpx;
  animation: float 10s ease-in-out infinite;
}

.circle-4 {
  width: 60rpx;
  height: 60rpx;
  top: 200rpx;
  right: 200rpx;
  background: rgba(255, 255, 255, 0.08);
  animation: float 7s ease-in-out infinite;
}

.circle-5 {
  width: 40rpx;
  height: 40rpx;
  bottom: 120rpx;
  left: 80rpx;
  background: rgba(255, 255, 255, 0.06);
  animation: float 9s ease-in-out infinite reverse;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-20px) rotate(180deg) scale(1.1);
  }
}

/* 用户信息卡片 */
.user-card {
  position: absolute;
  bottom: -30rpx;
  left: 30rpx;
  right: 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15), 0 8rpx 20rpx rgba(102, 126, 234, 0.1), inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20px);
}

.card-content {
  position: relative;
  z-index: 2;
  padding: 40rpx 30rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.card-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100rpx;
  height: 100rpx;
  overflow: hidden;
}

.decoration-dot {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.dot-1 {
  width: 20rpx;
  height: 20rpx;
  top: 20rpx;
  right: 30rpx;
  animation: dotFloat 4s ease-in-out infinite;
}

.dot-2 {
  width: 12rpx;
  height: 12rpx;
  top: 50rpx;
  right: 60rpx;
  animation: dotFloat 6s ease-in-out infinite reverse;
}

.dot-3 {
  width: 8rpx;
  height: 8rpx;
  top: 35rpx;
  right: 15rpx;
  animation: dotFloat 5s ease-in-out infinite;
}

@keyframes dotFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-8px) scale(1.2);
    opacity: 1;
  }
}

.user-avatar-container {
  position: relative;
}

.avatar-glow {
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
  animation: glowPulse 3s ease-in-out infinite;
  z-index: 0;
}

@keyframes glowPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.avatar-wrapper {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: v-bind(avatar) no-repeat center;
  background-size: cover;
  position: relative;
  z-index: 3;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15), inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
}

.avatar-ring {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  width: 132rpx;
  height: 132rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  z-index: 1;
  animation: ringRotate 8s linear infinite;
}

@keyframes ringRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.avatar-inner-ring {
  position: absolute;
  top: -3rpx;
  left: -3rpx;
  width: 126rpx;
  height: 126rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  z-index: 2;
}

.online-indicator {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  background: #10b981;
  border-radius: 50%;
  border: 4rpx solid #fff;
  z-index: 4;
  box-shadow: 0 4rpx 8rpx rgba(16, 185, 129, 0.3);
}

.pulse-ring {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #10b981;
  border-radius: 50%;
  animation: pulseRing 2s ease-out infinite;
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.user-info {
  flex: 1;
}

.user-name-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.user-badge {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3), inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;
  line-height: 24rpx;
}

.badge-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: badgeShine 3s ease-in-out infinite;
}

@keyframes badgeShine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

.badge-text {
  position: relative;
  z-index: 1;
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
}

.user-stats {
  margin-top: 8rpx;
}

.stat-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx 16rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
  margin-right: 16rpx;
}

.stat-value {
  font-size: 20rpx;
  font-weight: 600;
  color: #667eea;
  line-height: 1;
}

.stat-label {
  font-size: 18rpx;
  color: #8b9dc3;
  margin-top: 2rpx;
}

.time-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28rpx;
  height: 28rpx;
  background: rgba(139, 157, 195, 0.1);
  border-radius: 50%;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.login-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #8b9dc3;
}

/* 菜单区域 */
.menu-section {
  padding: 80rpx 30rpx 40rpx;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: calc(100vh - 600rpx);
  position: relative;
}

.section-header {
  margin-bottom: 30rpx;
  text-align: center;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8rpx 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 24rpx;
  color: #8b9dc3;
}

.menu-card {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12), 0 4rpx 12rpx rgba(102, 126, 234, 0.08), inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.menu-card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx 30rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  z-index: 2;

  &:active {
    transform: scale(0.98);
  }

  &:active .menu-item-background {
    opacity: 1;
    transform: scale(1.02);
  }
}

.menu-item-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: -1;
}

.menu-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  position: relative;
  overflow: hidden;

  &.test-icon {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    box-shadow: 0 8rpx 20rpx rgba(79, 70, 229, 0.3), inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
  }

  &.feedback-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 8rpx 20rpx rgba(16, 185, 129, 0.3), inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
  }

  &.logout-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 8rpx 20rpx rgba(239, 68, 68, 0.3), inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
  }
  &.record-icon {
    background: linear-gradient(135deg, #3695ee 0%, #1423f0 100%);
    box-shadow: 0 8rpx 20rpx rgba(239, 68, 68, 0.3), inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
  }
}

.icon-glow {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  border-radius: 20rpx;
  opacity: 0.6;
  animation: iconGlow 2s ease-in-out infinite alternate;
}

.test-glow {
  background: radial-gradient(circle, rgba(79, 70, 229, 0.3) 0%, transparent 70%);
}

.feedback-glow {
  background: radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, transparent 70%);
}

.logout-glow {
  background: radial-gradient(circle, rgba(239, 68, 68, 0.3) 0%, transparent 70%);
}

@keyframes iconGlow {
  0% {
    transform: scale(0.8);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.arrow-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: rgba(193, 201, 210, 0.1);
  transition: all 0.3s ease;
}

.menu-item:active .arrow-wrapper {
  background: rgba(102, 126, 234, 0.2);
  transform: translateX(4rpx);
}

.menu-content {
  flex: 1;

  /deep/.wd-badge__content {
    height: auto;
  }
}

.menu-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;

  &.logout-text {
    color: #ef4444;
  }
}

.menu-desc {
  font-size: 24rpx;
  color: #9ca3af;
  line-height: 1.4;
}

.menu-divider {
  position: relative;
  height: 1rpx;
  margin: 0 30rpx;
  overflow: hidden;
}

.divider-line {
  height: 1rpx;
  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
  position: relative;
}

.divider-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
  animation: dividerShine 3s ease-in-out infinite;
}

@keyframes dividerShine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* 底部装饰 */
.bottom-decoration {
  position: relative;
  margin-top: 60rpx;
  height: 120rpx;
  overflow: hidden;
}

.decoration-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 50% 50% 0 0;
  animation: waveFloat 4s ease-in-out infinite;
}

@keyframes waveFloat {
  0%,
  100% {
    transform: translateY(0px) scaleX(1);
  }
  50% {
    transform: translateY(-10px) scaleX(1.05);
  }
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.floating-dot {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
}

.dot-a {
  width: 16rpx;
  height: 16rpx;
  top: 30rpx;
  left: 20%;
  animation: floatA 6s ease-in-out infinite;
}

.dot-b {
  width: 12rpx;
  height: 12rpx;
  top: 50rpx;
  right: 30%;
  animation: floatB 8s ease-in-out infinite;
}

.dot-c {
  width: 10rpx;
  height: 10rpx;
  top: 20rpx;
  right: 20%;
  animation: floatC 7s ease-in-out infinite;
}

@keyframes floatA {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes floatB {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-15px) rotate(-180deg);
    opacity: 0.8;
  }
}

@keyframes floatC {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-25px) rotate(360deg);
    opacity: 0.9;
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .user-card {
    padding: 30rpx 20rpx 20rpx;
    gap: 20rpx;
  }

  .avatar-wrapper {
    width: 100rpx;
    height: 100rpx;
  }

  .avatar {
    width: 100rpx;
    height: 100rpx;
  }

  .avatar-ring {
    width: 112rpx;
    height: 112rpx;
  }

  .user-name {
    font-size: 32rpx;
  }

  .menu-item {
    padding: 28rpx 24rpx;
  }

  .menu-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 20rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .menu-section {
    background: #111827;
  }

  .menu-card {
    background: #1f2937;
  }

  .menu-title {
    color: #f9fafb;
  }

  .menu-desc {
    color: #6b7280;
  }

  .menu-divider {
    background: #374151;
  }

  .menu-item:active {
    background: #374151;
  }
}
</style>
