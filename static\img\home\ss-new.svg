<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#waterGradient)" stroke="url(#waterBorder)" stroke-width="2"/>
  
  <!-- 水务所图标 -->
  <g transform="translate(12, 12)">
    <!-- 建筑主体 -->
    <rect x="8" y="16" width="24" height="20" fill="white" rx="2" opacity="0.9"/>
    <rect x="10" y="18" width="20" height="16" fill="none" stroke="white" stroke-width="1.5" opacity="0.7"/>
    
    <!-- 屋顶 -->
    <path d="M4 20 L20 8 L36 20 L32 20 L20 12 L8 20 Z" fill="white"/>
    
    <!-- 水滴装饰 -->
    <g transform="translate(20, 22)">
      <path d="M0 8 C-4 4, -4 0, 0 0 C4 0, 4 4, 0 8 Z" fill="url(#dropGradient)"/>
      <circle cx="0" cy="6" r="1" fill="white" opacity="0.8"/>
    </g>
    
    <!-- 窗户 -->
    <rect x="12" y="20" width="3" height="3" fill="url(#waterGradient)" opacity="0.6" rx="0.5"/>
    <rect x="17" y="20" width="3" height="3" fill="url(#waterGradient)" opacity="0.6" rx="0.5"/>
    <rect x="22" y="20" width="3" height="3" fill="url(#waterGradient)" opacity="0.6" rx="0.5"/>
    <rect x="27" y="20" width="3" height="3" fill="url(#waterGradient)" opacity="0.6" rx="0.5"/>
    
    <rect x="12" y="26" width="3" height="3" fill="url(#waterGradient)" opacity="0.6" rx="0.5"/>
    <rect x="17" y="26" width="3" height="3" fill="url(#waterGradient)" opacity="0.6" rx="0.5"/>
    <rect x="22" y="26" width="3" height="3" fill="url(#waterGradient)" opacity="0.6" rx="0.5"/>
    <rect x="27" y="26" width="3" height="3" fill="url(#waterGradient)" opacity="0.6" rx="0.5"/>
    
    <!-- 门 -->
    <rect x="18" y="30" width="4" height="6" fill="white" rx="2"/>
    <circle cx="21" cy="33" r="0.5" fill="url(#waterGradient)"/>
  </g>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="waterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="waterBorder" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2980b9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1f4e79;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="dropGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#3498db;stop-opacity:0.8" />
    </radialGradient>
  </defs>
</svg>
