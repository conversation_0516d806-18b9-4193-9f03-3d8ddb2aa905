<template>
  <div class="pipeline-table-modern">
    <!-- 标题区域 -->
    <div class="table-title" @click="toggleCollapse">
      <div class="title-icon">
        <div class="icon-circle">
          <div class="icon-pipe"></div>
        </div>
      </div>
      <div class="title-text">
        <h3>排水管渠统计</h3>
        <p>管道类型数据总览</p>
      </div>
      <div class="collapse-button">
        <wd-icon :name="isCollapsed ? 'chevron-down' : 'chevron-up'" size="20rpx" color="white" />
      </div>
    </div>

    <!-- 数据卡片区域 -->
    <div class="data-cards" :class="{ collapsed: isCollapsed }">
      <template v-for="(item, index) in pipelineData" :key="index">
        <div class="data-card">
          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="type-badge" :class="`type-${index + 1}`">
              <span class="type-icon">{{ getTypeIcon(item.key) }}</span>
              <span class="type-name">{{ item.key }}</span>
            </div>
            <div class="total-value">
              <span class="value">{{ formatNumber(item.total) }}</span>
              <span class="unit">{{ getUnit(item) }}</span>
            </div>
          </div>

          <!-- 数据条形图 -->
          <div class="data-bars">
            <div class="bar-item municipal">
              <div class="bar-label">
                <span class="label-text">市政</span>
                <span class="label-value">{{ formatNumber(item.municipal) }}</span>
              </div>
              <div class="bar-track">
                <div class="bar-fill" :style="{ width: getMunicipalPercentage(item) + '%' }"></div>
              </div>
            </div>

            <div class="bar-item plot">
              <div class="bar-label">
                <span class="label-text">小区</span>
                <span class="label-value">{{ formatNumber(item.plot) }}</span>
              </div>
              <div class="bar-track">
                <div class="bar-fill" :style="{ width: getPlotPercentage(item) + '%' }"></div>
              </div>
            </div>
          </div>

          <!-- 比例指示器 -->
          <div class="ratio-indicator">
            <div class="ratio-item municipal">
              <div class="ratio-dot"></div>
              <span>市政 {{ getMunicipalPercentage(item).toFixed(1) }}%</span>
            </div>
            <div class="ratio-item plot">
              <div class="ratio-dot"></div>
              <span>小区 {{ getPlotPercentage(item).toFixed(1) }}%</span>
            </div>
          </div>
        </div>
      </template>
      <slot></slot>
    </div>

    <!-- 总计卡片 -->
    <div class="summary-card" @click="handleClick">
      <div class="summary-header">
        <div class="summary-icon">
          <div class="icon-total">∑</div>
        </div>
        <div class="summary-title">
          <h4>排水管渠合计</h4>
          <p>所有类型统计总和</p>
        </div>
      </div>

      <div class="summary-stats">
        <div class="summary-item municipal">
          <div class="summary-value">
            <span class="value">{{ formatNumber(totalSummary.municipal) }}</span>
            <span class="unit">km</span>
          </div>
          <div class="summary-label">市政总计</div>
        </div>
        <div class="summary-divider"></div>
        <div class="summary-item plot">
          <div class="summary-value">
            <span class="value">{{ formatNumber(totalSummary.plot) }}</span>
            <span class="unit">km</span>
          </div>
          <div class="summary-label">小区总计</div>
        </div>
        <div class="summary-divider"></div>
        <div class="summary-item total">
          <div class="summary-value">
            <span class="value">{{ formatNumber(totalSummary.total) }}</span>
            <span class="unit">km</span>
          </div>
          <div class="summary-label">总计</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const emit = defineEmits(['toggleCollapse'])

const props = defineProps({
  pipelineData: {
    type: Array,
    default: () => []
  },
  totalSummary: {
    type: Object,
    default: () => ({ municipal: 0, plot: 0, total: 0 })
  }
})

// 折叠状态管理
const isCollapsed = ref(true) // 默认折叠

// 切换折叠状态
const toggleCollapse = () => {
  uni.vibrateShort({ type: 'medium' })
  isCollapsed.value = !isCollapsed.value
}

// 格式化数字显示
const formatNumber = (value) => {
  if (typeof value !== 'number') return '0.00'
  return value.toFixed(2)
}

// 获取类型图标
const getTypeIcon = (type) => {
  const iconMap = {
    雨水管: '🌧️',
    污水管: '💧',
    合流管: '🔄',
    给水管: '🚰',
    燃气管: '🔥',
    电力管: '⚡',
    通信管: '📡'
  }
  return iconMap[type] || '🔧'
}

// 获取单位
const getUnit = (item) => {
  // 根据数据判断单位，如果有小数点通常是km，整数通常是个数
  const hasDecimal = item.total % 1 !== 0
  return hasDecimal ? 'km' : '个'
}

// 计算市政占比
const getMunicipalPercentage = (item) => {
  if (item.total === 0) return 0
  return (item.municipal / item.total) * 100
}

// 计算小区占比
const getPlotPercentage = (item) => {
  if (item.total === 0) return 0
  return (item.plot / item.total) * 100
}

function handleClick() {
  emit('toggleCollapse', { key: '福田汇总数据' })
}
</script>

<style lang="less" scoped>
.pipeline-table-modern {
  margin-bottom: 32rpx;
}

// 标题区域
.table-title {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx 16rpx 0 0;
  color: white;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2rpx);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .title-icon {
    margin-right: 20rpx;
    z-index: 1;

    .icon-circle {
      width: 60rpx;
      height: 60rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10rpx);

      .icon-pipe {
        width: 24rpx;
        height: 24rpx;
        background: white;
        border-radius: 2rpx;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: -6rpx;
          right: -6rpx;
          height: 4rpx;
          background: white;
          border-radius: 2rpx;
          transform: translateY(-50%);
        }
      }
    }
  }

  .title-text {
    flex: 1;
    z-index: 1;

    h3 {
      font-size: 32rpx;
      font-weight: 700;
      margin: 0 0 8rpx 0;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }

    p {
      font-size: 24rpx;
      margin: 0;
      opacity: 0.9;
    }
  }

  .collapse-button {
    width: 48rpx;
    height: 48rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10rpx);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(1.1);
    }
  }

  .title-stats {
    z-index: 1;

    .stat-item {
      text-align: center;

      .stat-number {
        display: block;
        font-size: 36rpx;
        font-weight: 700;
        line-height: 1;
      }

      .stat-label {
        font-size: 20rpx;
        opacity: 0.8;
      }
    }
  }
}

// 数据卡片区域
.data-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%);
  max-height: none; // 移除最大高度限制，让内容自然展开
  overflow: visible; // 改为可见，避免内容被裁剪
  opacity: 1;
  transition: all 0.4s ease-in-out;
  transform: scaleY(1);
  transform-origin: top;

  &.collapsed {
    max-height: 0;
    padding: 0 24rpx;
    opacity: 0;
    transform: scaleY(0);
    overflow: hidden; // 折叠时保持隐藏
  }
}

.data-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .type-badge {
      display: flex;
      align-items: center;
      gap: 12rpx;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-weight: 600;

      &.type-1 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      &.type-2 {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
      }

      .type-icon {
        font-size: 24rpx;
      }

      .type-name {
        font-size: 26rpx;
      }
    }

    .total-value {
      text-align: right;

      .value {
        font-size: 36rpx;
        font-weight: 700;
        color: #2c3e50;
        display: block;
        line-height: 1;
      }

      .unit {
        font-size: 20rpx;
        color: #7f8c8d;
        margin-left: 4rpx;
      }
    }
  }

  .data-bars {
    margin-bottom: 16rpx;

    .bar-item {
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .bar-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8rpx;

        .label-text {
          font-size: 24rpx;
          color: #34495e;
          font-weight: 500;
        }

        .label-value {
          font-size: 24rpx;
          font-weight: 600;
          color: #2c3e50;
        }
      }

      .bar-track {
        height: 8rpx;
        background: #ecf0f1;
        border-radius: 4rpx;
        overflow: hidden;

        .bar-fill {
          height: 100%;
          border-radius: 4rpx;
          transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }

      &.municipal .bar-fill {
        background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
      }

      &.plot .bar-fill {
        background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
      }
    }
  }

  .ratio-indicator {
    display: flex;
    gap: 24rpx;

    .ratio-item {
      display: flex;
      align-items: center;
      gap: 8rpx;
      font-size: 22rpx;
      color: #7f8c8d;

      .ratio-dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
      }

      &.municipal .ratio-dot {
        background: #3498db;
      }

      &.plot .ratio-dot {
        background: #e74c3c;
      }
    }
  }
}

// 总计卡片
.summary-card {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border-radius: 0 0 16rpx 16rpx;
  padding: 32rpx 24rpx;
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  }

  .summary-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    z-index: 1;
    position: relative;

    .summary-icon {
      width: 56rpx;
      height: 56rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;
      backdrop-filter: blur(10rpx);

      .icon-total {
        font-size: 28rpx;
        font-weight: 700;
        color: white;
      }
    }

    .summary-title {
      h4 {
        font-size: 28rpx;
        font-weight: 700;
        margin: 0 0 4rpx 0;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
      }

      p {
        font-size: 22rpx;
        margin: 0;
        opacity: 0.8;
      }
    }
  }

  .summary-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 1;
    position: relative;

    .summary-item {
      text-align: center;
      flex: 1;

      .summary-value {
        font-size: 32rpx;
        font-weight: 700;
        margin-bottom: 8rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: baseline;
        justify-content: center;
        gap: 4rpx;

        .value {
          font-size: 32rpx;
          font-weight: 700;
        }

        .unit {
          font-size: 20rpx;
          font-weight: 500;
          opacity: 0.8;
        }
      }

      .summary-label {
        font-size: 20rpx;
        opacity: 0.8;
      }

      &.municipal .summary-value .value {
        color: #74b9ff;
      }

      &.plot .summary-value .value {
        color: #fd79a8;
      }

      &.total .summary-value .value {
        color: #00b894;
      }
    }

    .summary-divider {
      width: 1rpx;
      height: 60rpx;
      background: rgba(255, 255, 255, 0.2);
      margin: 0 16rpx;
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .table-title {
    padding: 20rpx;

    .title-icon .icon-circle {
      width: 48rpx;
      height: 48rpx;
    }

    .title-text h3 {
      font-size: 28rpx;
    }

    .title-text p {
      font-size: 20rpx;
    }

    .collapse-button {
      width: 40rpx;
      height: 40rpx;
    }

    .title-stats .stat-number {
      font-size: 28rpx;
    }
  }

  .data-cards {
    padding: 16rpx;
    gap: 12rpx;
  }

  .data-card {
    padding: 20rpx;

    .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12rpx;

      .total-value .value {
        font-size: 28rpx;
      }
    }

    .ratio-indicator {
      flex-direction: column;
      gap: 8rpx;
    }
  }

  .summary-card {
    padding: 24rpx 20rpx;

    .summary-header {
      .summary-icon {
        width: 48rpx;
        height: 48rpx;
      }

      .summary-title h4 {
        font-size: 24rpx;
      }
    }

    .summary-stats {
      flex-direction: column;
      gap: 16rpx;

      .summary-divider {
        width: 60rpx;
        height: 1rpx;
        margin: 0;
      }

      .summary-item .summary-value {
        font-size: 28rpx;

        .value {
          font-size: 28rpx;
        }

        .unit {
          font-size: 18rpx;
        }
      }
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.data-card {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  &:nth-child(1) {
    animation-delay: 0.1s;
  }
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  &:nth-child(3) {
    animation-delay: 0.3s;
  }
}

.summary-card {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation-delay: 0.4s;
  animation-fill-mode: both;
}
</style>
