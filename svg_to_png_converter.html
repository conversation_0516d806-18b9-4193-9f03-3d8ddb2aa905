<!DOCTYPE html>
<html>
<head>
    <title>现代化Tab图标转换器</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { 
            color: #07519d; 
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .icon-container { 
            border: 2px solid #e0e0e0; 
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        .icon-container h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-bottom: 15px;
        }
        .icon-preview div {
            text-align: center;
        }
        .icon-preview span {
            display: block;
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }
        canvas { 
            border: 1px solid #ddd; 
            border-radius: 4px;
            background: white;
        }
        .download-link { 
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 16px;
            background: #07519d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            transition: background 0.3s;
        }
        .download-link:hover {
            background: #0a5aa8;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .instructions h3 {
            color: #07519d;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 现代化Tab图标生成器</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            为您的GIS地图应用生成统一风格的现代化图标
        </p>
        
        <div id="icons-container" class="icon-grid"></div>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>点击下载链接获取PNG格式的图标文件</li>
                <li>将下载的图标文件放置到 <code>static/img/</code> 目录下</li>
                <li>更新 <code>pages.json</code> 中的图标路径</li>
                <li>图标尺寸为48x48像素，适合移动端显示</li>
            </ol>
        </div>
    </div>

    <script>
        const icons = {
            'home': {
                name: '首页',
                normal: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 6L8 18V40C8 41.1046 8.89543 42 10 42H18V30C18 28.8954 18.8954 28 20 28H28C29.1046 28 30 28.8954 30 30V42H38C39.1046 42 40 41.1046 40 40V18L24 6Z" fill="#F5F5F5" stroke="#707070" stroke-width="2" stroke-linejoin="round"/>
                    <rect x="18" y="30" width="12" height="12" fill="white" stroke="#707070" stroke-width="1.5"/>
                    <circle cx="27" cy="36" r="1" fill="#707070"/>
                    <rect x="12" y="22" width="6" height="6" rx="1" fill="white" stroke="#707070" stroke-width="1.5"/>
                    <path d="M15 22V28M12 25H18" stroke="#707070" stroke-width="1"/>
                    <rect x="30" y="22" width="6" height="6" rx="1" fill="white" stroke="#707070" stroke-width="1.5"/>
                    <path d="M33 22V28M30 25H36" stroke="#707070" stroke-width="1"/>
                    <path d="M6 18L24 4L42 18" stroke="#707070" stroke-width="2" stroke-linecap="round"/>
                    <rect x="22" y="8" width="4" height="6" fill="#E0E0E0" stroke="#707070" stroke-width="1"/>
                </svg>`,
                active: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 6L8 18V40C8 41.1046 8.89543 42 10 42H18V30C18 28.8954 18.8954 28 20 28H28C29.1046 28 30 28.8954 30 30V42H38C39.1046 42 40 41.1046 40 40V18L24 6Z" fill="#E3F2FD" stroke="#07519d" stroke-width="2" stroke-linejoin="round"/>
                    <rect x="18" y="30" width="12" height="12" fill="white" stroke="#07519d" stroke-width="1.5"/>
                    <circle cx="27" cy="36" r="1" fill="#07519d"/>
                    <rect x="12" y="22" width="6" height="6" rx="1" fill="#BBDEFB" stroke="#07519d" stroke-width="1.5"/>
                    <path d="M15 22V28M12 25H18" stroke="#07519d" stroke-width="1"/>
                    <rect x="30" y="22" width="6" height="6" rx="1" fill="#BBDEFB" stroke="#07519d" stroke-width="1.5"/>
                    <path d="M33 22V28M30 25H36" stroke="#07519d" stroke-width="1"/>
                    <path d="M6 18L24 4L42 18" stroke="#07519d" stroke-width="2" stroke-linecap="round"/>
                    <rect x="22" y="8" width="4" height="6" fill="#90CAF9" stroke="#07519d" stroke-width="1"/>
                </svg>`
            },
            'record': {
                name: '档案',
                normal: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6 12C6 10.8954 6.89543 10 8 10H18L22 14H40C41.1046 14 42 14.8954 42 16V38C42 39.1046 41.1046 40 40 40H8C6.89543 40 6 39.1046 6 38V12Z" fill="#F5F5F5" stroke="#707070" stroke-width="2"/>
                    <path d="M18 10L22 14H42" fill="#E8E8E8" stroke="#707070" stroke-width="1.5"/>
                    <rect x="12" y="20" width="8" height="10" rx="1" fill="white" stroke="#707070" stroke-width="1.5"/>
                    <path d="M14 23H18M14 25H18M14 27H16" stroke="#707070" stroke-width="1"/>
                    <rect x="22" y="20" width="8" height="10" rx="1" fill="white" stroke="#707070" stroke-width="1.5"/>
                    <path d="M24 23H28M24 25H28M24 27H26" stroke="#707070" stroke-width="1"/>
                    <rect x="32" y="20" width="8" height="10" rx="1" fill="white" stroke="#707070" stroke-width="1.5"/>
                    <path d="M34 23H38M34 25H38M34 27H36" stroke="#707070" stroke-width="1"/>
                    <path d="M10 32H38" stroke="#707070" stroke-width="1" stroke-dasharray="2,2"/>
                </svg>`,
                active: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6 12C6 10.8954 6.89543 10 8 10H18L22 14H40C41.1046 14 42 14.8954 42 16V38C42 39.1046 41.1046 40 40 40H8C6.89543 40 6 39.1046 6 38V12Z" fill="#E3F2FD" stroke="#07519d" stroke-width="2"/>
                    <path d="M18 10L22 14H42" fill="#BBDEFB" stroke="#07519d" stroke-width="1.5"/>
                    <rect x="12" y="20" width="8" height="10" rx="1" fill="white" stroke="#07519d" stroke-width="1.5"/>
                    <path d="M14 23H18M14 25H18M14 27H16" stroke="#07519d" stroke-width="1"/>
                    <rect x="22" y="20" width="8" height="10" rx="1" fill="white" stroke="#07519d" stroke-width="1.5"/>
                    <path d="M24 23H28M24 25H28M24 27H26" stroke="#07519d" stroke-width="1"/>
                    <rect x="32" y="20" width="8" height="10" rx="1" fill="white" stroke="#07519d" stroke-width="1.5"/>
                    <path d="M34 23H38M34 25H38M34 27H36" stroke="#07519d" stroke-width="1"/>
                    <path d="M10 32H38" stroke="#07519d" stroke-width="1" stroke-dasharray="2,2"/>
                </svg>`
            },
            'pump': {
                name: '泵房',
                normal: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="8" y="20" width="24" height="20" rx="2" fill="#F5F5F5" stroke="#707070" stroke-width="2"/>
                    <path d="M6 20L20 8L34 20" fill="#E8E8E8" stroke="#707070" stroke-width="2" stroke-linejoin="round"/>
                    <circle cx="20" cy="30" r="5" fill="#E0E0E0" stroke="#707070" stroke-width="2"/>
                    <circle cx="20" cy="30" r="2" fill="#707070"/>
                    <rect x="32" y="24" width="10" height="3" rx="1.5" fill="#D0D0D0" stroke="#707070" stroke-width="1.5"/>
                    <rect x="32" y="29" width="10" height="3" rx="1.5" fill="#D0D0D0" stroke="#707070" stroke-width="1.5"/>
                    <rect x="32" y="34" width="10" height="3" rx="1.5" fill="#D0D0D0" stroke="#707070" stroke-width="1.5"/>
                    <circle cx="32" cy="25.5" r="1.5" fill="#B0B0B0" stroke="#707070" stroke-width="1"/>
                    <circle cx="32" cy="30.5" r="1.5" fill="#B0B0B0" stroke="#707070" stroke-width="1"/>
                    <circle cx="32" cy="35.5" r="1.5" fill="#B0B0B0" stroke="#707070" stroke-width="1"/>
                    <rect x="14" y="32" width="4" height="8" fill="white" stroke="#707070" stroke-width="1.5"/>
                    <circle cx="17" cy="36" r="0.5" fill="#707070"/>
                    <rect x="24" y="24" width="4" height="4" rx="0.5" fill="white" stroke="#707070" stroke-width="1"/>
                    <path d="M26 24V28M24 26H28" stroke="#707070" stroke-width="0.5"/>
                    <rect x="22" y="8" width="3" height="8" fill="#D0D0D0" stroke="#707070" stroke-width="1"/>
                </svg>`,
                active: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="8" y="20" width="24" height="20" rx="2" fill="#E3F2FD" stroke="#07519d" stroke-width="2"/>
                    <path d="M6 20L20 8L34 20" fill="#BBDEFB" stroke="#07519d" stroke-width="2" stroke-linejoin="round"/>
                    <circle cx="20" cy="30" r="5" fill="#90CAF9" stroke="#07519d" stroke-width="2"/>
                    <circle cx="20" cy="30" r="2" fill="#07519d"/>
                    <rect x="32" y="24" width="10" height="3" rx="1.5" fill="#64B5F6" stroke="#07519d" stroke-width="1.5"/>
                    <rect x="32" y="29" width="10" height="3" rx="1.5" fill="#64B5F6" stroke="#07519d" stroke-width="1.5"/>
                    <rect x="32" y="34" width="10" height="3" rx="1.5" fill="#64B5F6" stroke="#07519d" stroke-width="1.5"/>
                    <circle cx="32" cy="25.5" r="1.5" fill="#42A5F5" stroke="#07519d" stroke-width="1"/>
                    <circle cx="32" cy="30.5" r="1.5" fill="#42A5F5" stroke="#07519d" stroke-width="1"/>
                    <circle cx="32" cy="35.5" r="1.5" fill="#42A5F5" stroke="#07519d" stroke-width="1"/>
                    <rect x="14" y="32" width="4" height="8" fill="white" stroke="#07519d" stroke-width="1.5"/>
                    <circle cx="17" cy="36" r="0.5" fill="#07519d"/>
                    <rect x="24" y="24" width="4" height="4" rx="0.5" fill="white" stroke="#07519d" stroke-width="1"/>
                    <path d="M26 24V28M24 26H28" stroke="#07519d" stroke-width="0.5"/>
                    <rect x="22" y="8" width="3" height="8" fill="#90CAF9" stroke="#07519d" stroke-width="1"/>
                </svg>`
            },
            'mine': {
                name: '我的',
                normal: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="24" cy="18" r="10" fill="#F5F5F5" stroke="#707070" stroke-width="2"/>
                    <circle cx="24" cy="16" r="5" fill="#E0E0E0" stroke="#707070" stroke-width="1.5"/>
                    <circle cx="22" cy="15" r="1" fill="#707070"/>
                    <circle cx="26" cy="15" r="1" fill="#707070"/>
                    <path d="M21 18C21 18 22.5 19 24 19C25.5 19 27 18 27 18" stroke="#707070" stroke-width="1" stroke-linecap="round"/>
                    <path d="M8 42C8 34 12 30 18 28C20 27 22 26.5 24 26.5C26 26.5 28 27 30 28C36 30 40 34 40 42" fill="#F5F5F5" stroke="#707070" stroke-width="2"/>
                    <path d="M18 28C20 29 22 30 24 30C26 30 28 29 30 28" stroke="#707070" stroke-width="1"/>
                    <circle cx="24" cy="32" r="1" fill="#707070"/>
                    <circle cx="24" cy="36" r="1" fill="#707070"/>
                    <path d="M18 28C16 30 14 32 12 34" stroke="#707070" stroke-width="2" stroke-linecap="round"/>
                    <path d="M30 28C32 30 34 32 36 34" stroke="#707070" stroke-width="2" stroke-linecap="round"/>
                </svg>`,
                active: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="24" cy="18" r="10" fill="#E3F2FD" stroke="#07519d" stroke-width="2"/>
                    <circle cx="24" cy="16" r="5" fill="#BBDEFB" stroke="#07519d" stroke-width="1.5"/>
                    <circle cx="22" cy="15" r="1" fill="#07519d"/>
                    <circle cx="26" cy="15" r="1" fill="#07519d"/>
                    <path d="M21 18C21 18 22.5 19 24 19C25.5 19 27 18 27 18" stroke="#07519d" stroke-width="1" stroke-linecap="round"/>
                    <path d="M8 42C8 34 12 30 18 28C20 27 22 26.5 24 26.5C26 26.5 28 27 30 28C36 30 40 34 40 42" fill="#E3F2FD" stroke="#07519d" stroke-width="2"/>
                    <path d="M18 28C20 29 22 30 24 30C26 30 28 29 30 28" stroke="#07519d" stroke-width="1"/>
                    <circle cx="24" cy="32" r="1" fill="#07519d"/>
                    <circle cx="24" cy="36" r="1" fill="#07519d"/>
                    <path d="M18 28C16 30 14 32 12 34" stroke="#07519d" stroke-width="2" stroke-linecap="round"/>
                    <path d="M30 28C32 30 34 32 36 34" stroke="#07519d" stroke-width="2" stroke-linecap="round"/>
                </svg>`
            }
        };

        function svgToPng(svgString, filename, iconName, state) {
            const container = document.getElementById('icons-container');
            let iconDiv = document.getElementById(`icon-${iconName}`);
            
            if (!iconDiv) {
                iconDiv = document.createElement('div');
                iconDiv.id = `icon-${iconName}`;
                iconDiv.className = 'icon-container';
                
                const title = document.createElement('h3');
                title.textContent = `${icons[iconName].name} (${iconName})`;
                iconDiv.appendChild(title);
                
                const previewDiv = document.createElement('div');
                previewDiv.className = 'icon-preview';
                previewDiv.id = `preview-${iconName}`;
                iconDiv.appendChild(previewDiv);
                
                const downloadDiv = document.createElement('div');
                downloadDiv.id = `downloads-${iconName}`;
                iconDiv.appendChild(downloadDiv);
                
                container.appendChild(iconDiv);
            }
            
            const previewDiv = document.getElementById(`preview-${iconName}`);
            const downloadDiv = document.getElementById(`downloads-${iconName}`);
            
            const canvasContainer = document.createElement('div');
            const canvas = document.createElement('canvas');
            canvas.width = 48;
            canvas.height = 48;
            const label = document.createElement('span');
            label.textContent = state === 'normal' ? '普通状态' : '激活状态';
            
            canvasContainer.appendChild(canvas);
            canvasContainer.appendChild(label);
            previewDiv.appendChild(canvasContainer);
            
            const ctx = canvas.getContext('2d');
            
            const img = new Image();
            const svgBlob = new Blob([svgString], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.clearRect(0, 0, 48, 48);
                ctx.drawImage(img, 0, 0, 48, 48);
                
                canvas.toBlob(function(blob) {
                    const downloadUrl = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = downloadUrl;
                    link.download = filename + '.png';
                    link.textContent = `下载 ${filename}.png`;
                    link.className = 'download-link';
                    downloadDiv.appendChild(link);
                }, 'image/png');
                
                URL.revokeObjectURL(url);
            };
            
            img.src = url;
        }

        // Generate all icons
        Object.keys(icons).forEach(iconName => {
            svgToPng(icons[iconName].normal, iconName, iconName, 'normal');
            svgToPng(icons[iconName].active, iconName + '_active', iconName, 'active');
        });
    </script>
</body>
</html>
