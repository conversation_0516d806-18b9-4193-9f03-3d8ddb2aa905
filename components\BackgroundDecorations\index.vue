<template>
  <div class="background-decorations">
    <!-- 浮动圆圈 -->
    <div class="floating-circles">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
      <div class="circle circle-4"></div>
      <div class="circle circle-5"></div>
      <div class="circle circle-6"></div>
    </div>

    <!-- 光线效果 -->
    <div class="light-rays">
      <div class="ray ray-1"></div>
      <div class="ray ray-2"></div>
      <div class="ray ray-3"></div>
      <div class="ray ray-4"></div>
    </div>

    <!-- 微粒效果 -->
    <div class="particles">
      <div class="particle particle-1"></div>
      <div class="particle particle-2"></div>
      <div class="particle particle-3"></div>
      <div class="particle particle-4"></div>
      <div class="particle particle-5"></div>
      <div class="particle particle-6"></div>
      <div class="particle particle-7"></div>
      <div class="particle particle-8"></div>
    </div>

    <!-- 网格效果 -->
    <div class="grid-effects">
      <!-- 主网格 -->
      <div class="main-grid"></div>
      <!-- 次网格 -->
      <div class="sub-grid"></div>
      <!-- 细网格 -->
      <div class="fine-grid"></div>
      <!-- 点状网格 -->
      <div class="dot-grid"></div>
      <!-- 对角线网格 -->
      <div class="diagonal-grid"></div>
      <!-- 强调网格线 -->
      <div class="accent-grid"></div>
    </div>
  </div>
</template>

<script setup>
// 背景装饰组件，提供丰富的视觉效果
// 包含浮动圆圈、光线效果、微粒效果和多层网格
</script>

<style lang="less" scoped>
// 背景装饰元素
.background-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  overflow: hidden;
}

// 浮动圆圈
.floating-circles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
}

.circle-1 {
  width: 180rpx;
  height: 180rpx;
  top: 10%;
  right: -50rpx;
  background: rgba(255, 255, 255, 0.2);
  animation: floatSlow 8s ease-in-out infinite;
}

.circle-2 {
  width: 120rpx;
  height: 120rpx;
  top: 25%;
  left: -30rpx;
  background: rgba(165, 180, 252, 0.25);
  animation: floatSlow 10s ease-in-out infinite reverse;
}

.circle-3 {
  width: 90rpx;
  height: 90rpx;
  top: 45%;
  right: 15%;
  background: rgba(249, 168, 212, 0.2);
  animation: floatSlow 12s ease-in-out infinite;
}

.circle-4 {
  width: 60rpx;
  height: 60rpx;
  top: 65%;
  left: 20%;
  background: rgba(255, 255, 255, 0.15);
  animation: floatSlow 9s ease-in-out infinite reverse;
}

.circle-5 {
  width: 200rpx;
  height: 200rpx;
  bottom: 10%;
  right: -80rpx;
  background: rgba(196, 181, 253, 0.18);
  animation: floatSlow 14s ease-in-out infinite;
}

.circle-6 {
  width: 40rpx;
  height: 40rpx;
  bottom: 30%;
  left: 10%;
  background: rgba(255, 255, 255, 0.12);
  animation: floatSlow 7s ease-in-out infinite reverse;
}

@keyframes floatSlow {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30rpx) rotate(180deg) scale(1.1);
    opacity: 1;
  }
}

// 光线效果
.light-rays {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.ray {
  position: absolute;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transform-origin: center;
}

.ray-1 {
  width: 2px;
  height: 200rpx;
  top: 15%;
  left: 25%;
  animation: rayRotate 15s linear infinite;
}

.ray-2 {
  width: 1px;
  height: 150rpx;
  top: 35%;
  right: 20%;
  animation: rayRotate 20s linear infinite reverse;
}

.ray-3 {
  width: 3px;
  height: 180rpx;
  bottom: 25%;
  left: 70%;
  animation: rayRotate 18s linear infinite;
}

.ray-4 {
  width: 1px;
  height: 120rpx;
  bottom: 40%;
  right: 40%;
  animation: rayRotate 22s linear infinite reverse;
}

@keyframes rayRotate {
  0% {
    transform: rotate(0deg) translateY(-20rpx);
    opacity: 0;
  }
  25% {
    opacity: 0.3;
  }
  75% {
    opacity: 0.3;
  }
  100% {
    transform: rotate(360deg) translateY(-20rpx);
    opacity: 0;
  }
}

// 微粒效果
.particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.4);
}

.particle-1 {
  top: 20%;
  left: 15%;
  animation: particleFloat 8s ease-in-out infinite;
}

.particle-2 {
  top: 35%;
  right: 25%;
  animation: particleFloat 12s ease-in-out infinite reverse;
  animation-delay: -2s;
}

.particle-3 {
  top: 50%;
  left: 30%;
  animation: particleFloat 10s ease-in-out infinite;
  animation-delay: -4s;
}

.particle-4 {
  bottom: 40%;
  right: 15%;
  animation: particleFloat 14s ease-in-out infinite reverse;
  animation-delay: -1s;
}

.particle-5 {
  bottom: 25%;
  left: 45%;
  animation: particleFloat 9s ease-in-out infinite;
  animation-delay: -3s;
}

.particle-6 {
  top: 15%;
  right: 40%;
  animation: particleFloat 11s ease-in-out infinite reverse;
  animation-delay: -5s;
}

.particle-7 {
  bottom: 60%;
  left: 60%;
  animation: particleFloat 13s ease-in-out infinite;
  animation-delay: -2.5s;
}

.particle-8 {
  top: 70%;
  right: 50%;
  animation: particleFloat 7s ease-in-out infinite reverse;
  animation-delay: -1.5s;
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-40rpx) translateX(20rpx) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-20rpx) translateX(-15rpx) scale(0.8);
    opacity: 1;
  }
  75% {
    transform: translateY(-60rpx) translateX(10rpx) scale(1.1);
    opacity: 0.6;
  }
}

// 网格效果
.grid-effects {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

// 主网格 - 大网格
.main-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(rgba(255, 255, 255, 0.15) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 80px 80px;
  animation: gridSlowMove 30s linear infinite;
}

// 次网格 - 中等网格
.sub-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(rgba(255, 255, 255, 0.08) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.08) 1px, transparent 1px);
  background-size: 40px 40px;
  animation: gridSlowMove 45s linear infinite reverse;
}

// 细网格 - 小网格
.fine-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: gridSlowMove 25s linear infinite;
}

// 点状网格
.dot-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.18) 2px, transparent 2px);
  background-size: 60px 60px;
  animation: gridSlowMove 35s linear infinite;
}

// 对角线网格
.diagonal-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(45deg, transparent 47%, rgba(255, 255, 255, 0.06) 48%, rgba(255, 255, 255, 0.06) 52%, transparent 53%),
    linear-gradient(-45deg, transparent 47%, rgba(255, 255, 255, 0.06) 48%, rgba(255, 255, 255, 0.06) 52%, transparent 53%);
  background-size: 100px 100px;
  animation: gridSlowMove 50s linear infinite reverse;
}

// 强调网格线 - 突出的主要分割线
.accent-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(rgba(255, 255, 255, 0.2) 2px, transparent 2px), linear-gradient(90deg, rgba(255, 255, 255, 0.2) 2px, transparent 2px);
  background-size: 160px 160px;
  animation: gridSlowMove 40s linear infinite reverse;
}

@keyframes gridSlowMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(80px, 80px);
  }
}
</style>
