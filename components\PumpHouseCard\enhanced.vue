<template>
  <div @click="handleClick" class="enhanced-card" :class="[getStatusClass(item.status), { 'card-alternate': index % 2 === 1 }]">
    <!-- 状态指示条 -->
    <div class="status-bar" :class="getStatusClass(item.status)"></div>

    <!-- 卡片主体 -->
    <div class="card-body">
      <!-- 头部区域 -->
      <div class="card-header">
        <div class="header-content">
          <div class="pump-icon">
            <wd-icon name="home" size="28px" color="#ffffff" />
          </div>
          <div class="pump-info">
            <h3 class="pump-title">{{ item.PumpHouseName || '未命名泵房' }}</h3>
            <div class="pump-meta">
              <span class="room-code">{{ item.RemouldState || '无数据' }}</span>
              <div class="status-chip" :class="getStatusClass(item.ProgressStatus)">
                <div class="status-dot"></div>
                <span>{{ getStatusText(item.ProgressStatus) }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="action-icon">
          <wd-icon name="arrow-right" size="20px" color="#c5d2d9" />
        </div>
      </div>

      <!-- 底部操作提示 -->
      <div class="card-footer">
        <div class="action-hint">
          <wd-icon name="eye" size="16px" color="#40a9ff" />
          <span>查看详情</span>
        </div>
      </div>
    </div>

    <!-- 悬浮效果 -->
    <div class="hover-overlay"></div>
  </div>
</template>

<script setup>
const props = defineProps({
  item: {
    type: Object,
    required: true,
    default: () => ({})
  },
  index: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['click'])

// 获取状态样式类
function getStatusClass(status) {
  const statusMap = {
    正常: 'status-normal',
    运行: 'status-running',
    滞后: 'status-maintenance',
    停工: 'status-error'
  }
  return statusMap[status] || 'status-unknown'
}

// 获取状态文本
function getStatusText(status) {
  const statusTextMap = {
    正常: '正常',
    运行: '运行中',
    维护: '维护中',
    滞后: '滞后',
    停工: '停工'
  }
  return statusTextMap[status] || '未知'
}

// 处理点击事件
function handleClick() {
  emit('click', props.item)
}
</script>

<style lang="less" scoped>
.enhanced-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  cursor: pointer;

  &:hover {
    transform: translateY(-6rpx);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);

    .hover-overlay {
      opacity: 1;
    }

    .action-icon {
      transform: translateX(6rpx);
    }
  }

  &:active {
    transform: translateY(-3rpx);
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  }

  &.card-alternate {
    background: rgba(248, 250, 252, 0.95);
  }
}

// 状态指示条
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;

  &.status-normal {
    background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  }

  &.status-running {
    background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  }

  &.status-maintenance {
    background: linear-gradient(90deg, #faad14 0%, #ffc53d 100%);
  }

  &.status-error {
    background: linear-gradient(90deg, #ff4d4f 0%, #ff7875 100%);
  }

  &.status-disabled {
    background: linear-gradient(90deg, #8c8c8c 0%, #bfbfbf 100%);
  }
}

.card-body {
  padding: 28rpx;
}

// 头部区域
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.header-content {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  flex: 1;
}

.pump-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 18rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
  flex-shrink: 0;
}

.pump-info {
  flex: 1;
  min-width: 0;
}

.pump-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 12rpx 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pump-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.room-code {
  font-size: 22rpx;
  color: #64748b;
  background: rgba(100, 116, 139, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-weight: 500;
}

.status-chip {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  font-weight: 600;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;

  &.status-normal {
    color: #52c41a;
    background: rgba(82, 196, 26, 0.1);
  }

  &.status-running {
    color: #1890ff;
    background: rgba(24, 144, 255, 0.1);
  }

  &.status-maintenance {
    color: #faad14;
    background: rgba(250, 173, 20, 0.1);
  }

  &.status-error {
    color: #ff4d4f;
    background: rgba(255, 77, 79, 0.1);
  }

  &.status-disabled {
    color: #8c8c8c;
    background: rgba(140, 140, 140, 0.1);
  }
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: currentColor;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.action-icon {
  width: 44rpx;
  height: 44rpx;
  border-radius: 12rpx;
  background: rgba(197, 210, 217, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

// 信息区域
.card-content {
  margin-bottom: 20rpx;
}

.info-row {
  margin-bottom: 12rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.info-label {
  font-size: 22rpx;
  color: #8e9aaf;
  font-weight: 500;
  min-width: 60rpx;
}

.info-value {
  font-size: 24rpx;
  color: #2d3748;
  font-weight: 600;
  flex: 1;
}

.address-row {
  margin-top: 16rpx;
}

.address-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  background: rgba(64, 169, 255, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(64, 169, 255, 0.1);
}

.address-text {
  font-size: 22rpx;
  color: #2d3748;
  line-height: 1.4;
  flex: 1;
}

// 底部操作
.card-footer {
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  padding-top: 16rpx;
}

.action-hint {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #40a9ff;
  font-weight: 500;
}

// 悬浮效果
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 169, 255, 0.03) 0%, rgba(102, 126, 234, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: 20rpx;
}

// 响应式设计
@media (max-width: 750rpx) {
  .enhanced-card {
    margin-bottom: 16rpx;
  }

  .card-body {
    padding: 24rpx;
  }

  .pump-icon {
    width: 60rpx;
    height: 60rpx;
  }

  .pump-title {
    font-size: 28rpx;
  }

  .info-item,
  .address-item {
    padding: 10rpx 14rpx;
  }
}
</style>
