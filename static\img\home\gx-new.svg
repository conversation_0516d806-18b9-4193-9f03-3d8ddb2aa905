<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#gridGradient)" stroke="url(#gridBorder)" stroke-width="2"/>
  
  <!-- 网格图标 -->
  <g transform="translate(16, 16)">
    <!-- 主网格线 -->
    <rect x="2" y="2" width="28" height="28" fill="none" stroke="white" stroke-width="2.5" rx="2"/>
    
    <!-- 垂直分割线 -->
    <line x1="12" y1="2" x2="12" y2="30" stroke="white" stroke-width="2" opacity="0.8"/>
    <line x1="20" y1="2" x2="20" y2="30" stroke="white" stroke-width="2" opacity="0.8"/>
    
    <!-- 水平分割线 -->
    <line x1="2" y1="12" x2="30" y2="12" stroke="white" stroke-width="2" opacity="0.8"/>
    <line x1="2" y1="20" x2="30" y2="20" stroke="white" stroke-width="2" opacity="0.8"/>
    
    <!-- 网格节点装饰 -->
    <circle cx="12" cy="12" r="1.5" fill="white" opacity="0.9"/>
    <circle cx="20" cy="12" r="1.5" fill="white" opacity="0.9"/>
    <circle cx="12" cy="20" r="1.5" fill="white" opacity="0.9"/>
    <circle cx="20" cy="20" r="1.5" fill="white" opacity="0.9"/>
    
    <!-- 中心标识 -->
    <circle cx="16" cy="16" r="3" fill="white" opacity="0.3"/>
    <circle cx="16" cy="16" r="1.5" fill="white"/>
  </g>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gridGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2ecc71;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gridBorder" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#229954;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#27ae60;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
