<template>
  <div @click="handleClick" class="pump-house-item" :class="{ 'item-alternate': index % 2 === 1 }">
    <div class="item-main">
      <div class="item-left">
        <div class="pump-house-avatar">
          <div class="avatar-bg">
            <image src="/static/img/home/<USER>" mode="aspectFit" class="avatar-img" />
          </div>
        </div>
        <div class="pump-house-details">
          <h4 class="pump-house-title">{{ item.PumpHouseName || '未命名泵房' }}</h4>
          <div class="pump-house-meta">
            <span class="meta-item">
              <wd-icon name="location" size="10px" color="#8e9aaf" />
              {{ item.PumpRoomNumber || '无编号' }}
            </span>
            <span class="meta-divider">•</span>
            <span class="meta-item">
              <wd-icon name="setting" size="10px" color="#8e9aaf" />
              {{ item.ProgressStatus || '状态未知' }}
            </span>
          </div>
          <div class="pump-house-info">
            <span class="info-tag" v-if="item.zoneCode">
              <wd-icon name="home" size="8px" color="#40a9ff" />
              {{ item.zoneCode }}
            </span>
            <span class="info-tag" v-if="item.address">
              <wd-icon name="location" size="8px" color="#52c41a" />
              {{ item.address }}
            </span>
          </div>
        </div>
      </div>
      <div class="item-right">
        <div class="status-indicator" :class="getStatusClass(item.status)">
          <div class="status-dot"></div>
        </div>
        <div class="action-arrow">
          <wd-icon name="arrow-right" size="16px" color="#c5d2d9" />
        </div>
      </div>
    </div>

    <!-- 底部操作区域 -->
    <div class="card-footer">
      <div class="footer-left">
        <div class="detail-info">
          <span class="detail-text">查看详情</span>
        </div>
      </div>
      <div class="footer-right">
        <span class="action-text">点击查看</span>
      </div>
    </div>

    <!-- 点击波纹效果 -->
    <div class="ripple-effect"></div>
  </div>
</template>

<script setup>
const props = defineProps({
  // 泵房数据
  item: {
    type: Object,
    required: true,
    default: () => ({})
  },
  // 列表索引
  index: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['click'])

// 获取状态样式类
function getStatusClass(status) {
  const statusMap = {
    正常: 'status-normal',
    运行: 'status-running',
    维护: 'status-maintenance',
    故障: 'status-error',
    停用: 'status-disabled'
  }
  return statusMap[status] || 'status-unknown'
}

// 处理点击事件
function handleClick() {
  emit('click', props.item)
}
</script>

<style lang="less" scoped>
// 泵房列表项样式
.pump-house-item {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  cursor: pointer;

  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
  }

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
  }

  &.item-alternate {
    background: rgba(248, 250, 252, 0.95);
  }
}

.item-main {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.item-left {
  display: flex;
  align-items: flex-start;
  flex: 1;
  gap: 16rpx;
}

// 泵房头像样式
.pump-house-avatar {
  flex-shrink: 0;
}

.avatar-bg {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(79, 172, 254, 0.3);
}

.avatar-img {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

// 泵房详情样式
.pump-house-details {
  flex: 1;
  min-width: 0;
}

.pump-house-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8rpx 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pump-house-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.2;
}

.meta-divider {
  color: #cbd5e0;
  font-size: 20rpx;
}

.pump-house-info {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.info-tag {
  display: flex;
  align-items: center;
  gap: 4rpx;
  background: rgba(64, 169, 255, 0.1);
  color: #40a9ff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(64, 169, 255, 0.2);
}

// 右侧状态和箭头
.item-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-shrink: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24rpx;
  height: 24rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #d9d9d9;
  transition: all 0.3s ease;
}

// 状态样式
.status-normal .status-dot {
  background: #52c41a;
  box-shadow: 0 0 8rpx rgba(82, 196, 26, 0.4);
}

.status-running .status-dot {
  background: #1890ff;
  box-shadow: 0 0 8rpx rgba(24, 144, 255, 0.4);
}

.status-maintenance .status-dot {
  background: #faad14;
  box-shadow: 0 0 8rpx rgba(250, 173, 20, 0.4);
}

.status-error .status-dot {
  background: #ff4d4f;
  box-shadow: 0 0 8rpx rgba(255, 77, 79, 0.4);
}

.status-disabled .status-dot {
  background: #8c8c8c;
  box-shadow: 0 0 8rpx rgba(140, 140, 140, 0.4);
}

.action-arrow {
  opacity: 0.6;
  transition: all 0.3s ease;
}

.pump-house-item:hover .action-arrow {
  opacity: 1;
  transform: translateX(4rpx);
}

// 底部操作区域
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.footer-left {
  display: flex;
  align-items: center;
}

.detail-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-text {
  font-size: 24rpx;
  color: #40a9ff;
  font-weight: 500;
}

.footer-right {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 24rpx;
  color: #999;
}

// 波纹效果
.ripple-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(64, 169, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
  pointer-events: none;
}

.pump-house-item:active .ripple-effect {
  opacity: 1;
  transform: scale(1);
}

// 响应式设计
@media (max-width: 750rpx) {
  .pump-house-item {
    padding: 20rpx;
    margin-bottom: 12rpx;
  }

  .pump-house-title {
    font-size: 28rpx;
  }

  .meta-item {
    font-size: 22rpx;
  }

  .avatar-bg {
    width: 60rpx;
    height: 60rpx;
  }

  .avatar-img {
    width: 30rpx;
    height: 30rpx;
  }
}
</style>
