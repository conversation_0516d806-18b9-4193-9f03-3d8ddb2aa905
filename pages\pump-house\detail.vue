<template>
  <div class="detail pad-X12">
    <!-- 优化的标题头部 -->
    <div class="pump-house-header">
      <div class="header-content">
        <div class="pump-house-info">
          <div class="pump-house-title">{{ detail?.PumpHouseName }}</div>
          <div class="status-info">
            <div class="status-badge" :class="getStatusClass(detail?.RemouldState)">
              {{ detail?.RemouldState }}
            </div>
            <div class="batch-info">{{ batchKeys[detail?.Batch] }}</div>
          </div>
        </div>

        <!-- 导航和分享按钮 -->
        <div class="action-buttons">
          <div @click="openNavigation" class="navigation-button" :class="{ loading: navigationLoading, disabled: !hasCoordinates }">
            <div class="nav-icon-wrapper">
              <wd-icon v-if="!navigationLoading" name="location" size="18px" color="#fff"></wd-icon>
              <div v-else class="loading-spinner"></div>
            </div>
          </div>

          <button style="padding: 0" open-type="share">
            <div class="share-icon-wrapper">
              <wd-icon name="share" size="18px" color="#fff"></wd-icon>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 基础信息 -->
    <div class="info-section" v-if="detail">
      <div class="section-title">
        <wd-icon name="home" size="16px" color="#4A90E2"></wd-icon>
        <span>基础信息</span>
      </div>
      <div class="info-content">
        <div class="info-row">
          <div class="info-item">
            <span class="label">改造状态</span>
            <span class="value" :class="[getStatusClass(detail.RemouldState), { 'empty-value': isEmpty(formatValue(detail.RemouldState)) }]">{{ formatValue(detail.RemouldState) }}</span>
          </div>
          <div class="info-item">
            <span class="label">泵房批次</span>
            <span class="value" :class="{ 'empty-value': isEmpty(formatValue(batchKeys[detail.Batch])) }">{{ formatValue(batchKeys[detail.Batch]) }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">加压供水户数</span>
            <span class="value highlight">{{ formatValue(detail.PressurizedHouseholds, '户') }}</span>
          </div>
          <div class="info-item">
            <span class="label">小区建设时间</span>
            <span class="value">{{ formatValue(detail.ConstructionTime) }}</span>
          </div>
        </div>
        <div class="info-row full">
          <div class="info-item">
            <span class="label">泵房运营管理状态</span>
            <span class="value">{{ formatValue(detail.PumpRoomControlledState) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 物业信息 -->
    <div class="info-section" v-if="detail">
      <div class="section-title">
        <wd-icon name="user" size="16px" color="#50C878"></wd-icon>
        <span>物业信息</span>
      </div>
      <div class="info-content">
        <div class="info-row">
          <div class="info-item">
            <span class="label">物业单位</span>
            <span class="value">{{ formatValue(detail.PropertyUnit) }}</span>
          </div>
          <div class="info-item">
            <span class="label">物业联系人</span>
            <span class="value">{{ formatValue(detail.ContactPerson) }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">物业电话</span>
            <span class="value phone-number">{{ formatValue(detail.PhoneNumber) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术参数 -->
    <div class="info-section" v-if="detail?.Adjustable && ['code', 'qianwensong'].includes(userInfo?.Username)">
      <div class="section-title">
        <wd-icon name="chart" size="16px" color="#8B5CF6"></wd-icon>
        <span>技术参数</span>
      </div>
      <div class="info-content">
        <div class="info-row full">
          <div class="info-item pressure-zone-status">
            <span class="label">
              <wd-icon name="chart" size="12px" color="#8B5CF6"></wd-icon>
              加压区状态
            </span>
            <span class="value status-text">{{ formatValue(detail.PressureZoneStatus) }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">泵房精确楼层</span>
            <span class="value">{{ formatValue(detail.PumpHouseExactFloor) }}</span>
          </div>
        </div>
        <!-- 分区数据综合表格 -->
        <div class="table-section">
          <div class="table-title">
            <wd-icon name="chart" size="14px" color="#8B5CF6"></wd-icon>
            <span>分区数据总览</span>
          </div>
          <div class="data-table">
            <div class="table-header">
              <div class="table-cell header-cell">区域</div>
              <div class="table-cell header-cell">最高楼层</div>
              <div class="table-cell header-cell">理论末端压力</div>
              <div class="table-cell header-cell">出水压力</div>
            </div>
            <div class="table-row">
              <div class="table-cell zone-cell low-zone">低区</div>
              <div class="table-cell data-cell">{{ formatValue(detail.LowZoneMaxFloor) }}</div>
              <div class="table-cell data-cell pressure-cell">{{ formatValue(detail.LowZoneTheoreticalEndPressure, 'MPa') }}</div>
              <div class="table-cell data-cell pressure-cell">{{ formatValue(detail.LowZoneOutletPressure, 'MPa') }}</div>
            </div>
            <div class="table-row">
              <div class="table-cell zone-cell mid-zone">中区</div>
              <div class="table-cell data-cell">{{ formatValue(detail.MidZoneMaxFloor) }}</div>
              <div class="table-cell data-cell pressure-cell">{{ formatValue(detail.MidZoneTheoreticalEndPressure, 'MPa') }}</div>
              <div class="table-cell data-cell pressure-cell">{{ formatValue(detail.MidZoneOutletPressure, 'MPa') }}</div>
            </div>
            <div class="table-row">
              <div class="table-cell zone-cell high-zone">高区</div>
              <div class="table-cell data-cell">{{ formatValue(detail.HighZoneMaxFloor) }}</div>
              <div class="table-cell data-cell pressure-cell">{{ formatValue(detail.HighZoneTheoreticalEndPressure, 'MPa') }}</div>
              <div class="table-cell data-cell pressure-cell">{{ formatValue(detail.HighZoneOutletPressure, 'MPa') }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 项目信息 -->
    <div class="info-section" v-if="detail">
      <div class="section-title">
        <wd-icon name="setting" size="16px" color="#FF6B6B"></wd-icon>
        <span>项目信息</span>
      </div>
      <div class="info-content">
        <div class="info-row">
          <div class="info-item">
            <span class="label">项目进展状态</span>
            <span class="value" :class="getProgressClass(detail.ProgressStatus)">{{ formatValue(detail.ProgressStatus) }}</span>
          </div>
          <div class="info-item">
            <span class="label">运营管理状态</span>
            <span class="value">{{ formatValue(detail.OperationManagementState) }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">施工单位</span>
            <span class="value">{{ formatValue(detail.ConstructionUnit) }}</span>
          </div>
          <div class="info-item important">
            <span class="label">
              <wd-icon name="warn-bold" size="12px" color="#FF6B6B"></wd-icon>
              现场监管责任人
            </span>
            <span class="value">{{ formatValue(detail.PersonInCharge) }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">临供停水事件数</span>
            <span class="value" :class="detail.TemporarySupplyEvents > 0 ? 'warning' : 'success'">{{ formatValue(detail.TemporarySupplyEvents, '次') }}</span>
          </div>
          <div class="info-item">
            <span class="label">初步验收时间</span>
            <span class="value">{{ formatValue(detail.AcceptanceTime) }}</span>
          </div>
        </div>
        <div class="info-row full">
          <div class="info-item">
            <span class="label">备注</span>
            <span class="value remark">{{ formatValue(detail.Remark) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 位置信息 -->
    <div class="info-section" v-if="detail">
      <div class="section-title">
        <wd-icon name="location" size="16px" color="#9B59B6"></wd-icon>
        <span>位置信息</span>
      </div>
      <div class="info-content">
        <div class="coordinate-display">
          <div class="coordinate-header">
            <wd-icon name="location" size="16px" color="#0ea5e9"></wd-icon>
            <span class="coordinate-label">坐标位置</span>
          </div>
          <div class="coordinate-content" v-if="detail.X && detail.Y">
            <div class="coordinate-item">
              <span class="coordinate-type">经度</span>
              <span class="coordinate-value">{{ formatCoordinateValue(detail.X) }}</span>
            </div>
            <div class="coordinate-separator">|</div>
            <div class="coordinate-item">
              <span class="coordinate-type">纬度</span>
              <span class="coordinate-value">{{ formatCoordinateValue(detail.Y) }}</span>
            </div>
          </div>
          <div class="coordinate-empty" v-else>
            <span class="empty-value">暂无坐标信息</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">所属街道</span>
            <span class="value">{{ formatValue(detail.BelongingStreet) }}</span>
          </div>
          <div class="info-item">
            <span class="label">所属片区</span>
            <span class="value">{{ formatValue(detail.BelongingArea) }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">所属网格</span>
            <span class="value">{{ formatValue(detail.Gridding) }}</span>
          </div>
        </div>
        <div class="info-row full">
          <div class="info-item">
            <span class="label">小区地址</span>
            <span class="value">{{ formatValue(detail.ResidentialAddress) }}</span>
          </div>
        </div>
        <div class="info-row full">
          <div class="info-item">
            <span class="label">泵房精确位置</span>
            <span class="value">{{ formatValue(detail.AccuratePosition) }}</span>
          </div>
        </div>

        <!-- 泵房图片 -->
        <div class="image-section" v-if="detail.PumpHouseImg">
          <div class="image-title">
            <wd-icon name="camera" size="16px" color="#9B59B6"></wd-icon>
            <span>泵房图片</span>
          </div>
          <div class="image-grid">
            <template v-for="item in (detail?.PumpHouseImg ?? '').split(',')" :key="item">
              <div class="image-item" v-if="item" @click="previewImage(item)">
                <image class="pump-image" :src="item.replace('http:', 'https:')"></image>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <wd-toast />
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app'
import { PumpHouseApi } from '@/services/model/pump.house'
import * as CommonApi from '@/services/model/common.js'
import { useToast } from 'wot-design-uni'
import { cache } from '@/utils/cache.js'

onLoad(({ pumpRoomNumber }) => getPumpHouseDetail(pumpRoomNumber))

const detail = ref(null)
const batchKeys = { 1: '已纳改:利源代建', 2: '已纳改:查漏补缺', 3: '需纳改', 4: '无需纳改', 5: '已纳改:应改未改' }

// 导航和分享相关变量
const toast = useToast()
const navigationLoading = ref(false)
const isWechatMiniProgram = ref(false)
const userInfo = cache.get('userInfo')

// 检测是否为微信小程序环境
try {
  isWechatMiniProgram.value = typeof wx !== 'undefined' && wx.getSystemInfoSync
} catch (e) {
  isWechatMiniProgram.value = false
}

// 检查是否有坐标信息
const hasCoordinates = computed(() => {
  return detail.value?.X && detail.value?.Y
})
async function getPumpHouseDetail(pumpRoomNumber) {
  try {
    toast.loading('正在加载...')
    const { data } = await PumpHouseApi.detail(pumpRoomNumber)
    detail.value = data
    toast.close()
  } catch (error) {
    toast.close()
    toast.error('详情数据加载失败')
  }
}

// 新增的辅助函数
function getStatusClass(status) {
  const statusMap = {
    已完成: 'status-completed',
    进行中: 'status-progress',
    未开始: 'status-pending',
    异常: 'status-error'
  }
  return statusMap[status] || 'status-default'
}

function getProgressClass(progress) {
  const progressMap = {
    正常: 'progress-normal',
    延期: 'progress-delayed',
    异常: 'progress-error'
  }
  return progressMap[progress] || 'progress-default'
}

// 处理空值显示的辅助函数
function formatValue(value, suffix = '') {
  if (value === null || value === undefined || value === '') {
    return '--'
  }
  // 对于数字0，如果有后缀则显示，否则显示0
  if (value === 0) {
    return suffix ? `${value}${suffix}` : value
  }
  return suffix ? `${value}${suffix}` : value
}

// 检查值是否为空
function isEmpty(value) {
  return value === null || value === undefined || value === '' || value === '--'
}

// 处理坐标显示
function formatCoordinate(x, y) {
  if (!x || !y) {
    return '--'
  }
  return `${x}, ${y}`
}

// 格式化单个坐标值，保留6位小数
function formatCoordinateValue(value) {
  if (!value) {
    return '--'
  }
  const num = parseFloat(value)
  if (isNaN(num)) {
    return '--'
  }
  return num
}

function previewImage(url) {
  uni.previewImage({
    urls: detail.value.PumpHouseImg.split(','), // 需要预览的图片链接列表
    current: url // 当前显示图片的链接
  })
}

// 导航功能
async function openNavigation() {
  uni.vibrateShort({ type: 'medium' })
  try {
    // 检查是否有坐标信息
    if (!hasCoordinates.value) {
      return toast.warning('暂无坐标信息')
    }

    // 防止重复点击
    if (navigationLoading.value) {
      return
    }

    // 设置加载状态
    navigationLoading.value = true

    // 显示加载提示
    uni.showLoading({
      title: '正在获取位置...',
      mask: true
    })

    const originalLng = detail.value.X
    const originalLat = detail.value.Y

    // 验证坐标有效性
    if (isNaN(originalLng) || isNaN(originalLat)) {
      uni.hideLoading()
      return toast.error('坐标信息格式错误')
    }

    // 转换成高德坐标（GCJ02坐标系）
    const { data } = await CommonApi.coordinateTransformation({
      lng: originalLng,
      lat: originalLat,
      fn: 'wgs2gcj'
    })

    // 计算转换后的坐标
    const lng = Number(originalLng) + Number(data.lng.replace(originalLng, ''))
    const lat = Number(originalLat) + Number(data.lat.replace(originalLat, ''))

    uni.hideLoading()

    // 检测是否为微信小程序环境
    if (isWechatMiniProgram.value) {
      // 使用微信小程序原生导航API
      wx.openLocation({
        latitude: lat,
        longitude: lng,
        scale: 16, // 地图缩放级别，范围5-18
        name: detail.value.PumpHouseName || '泵房位置',
        address: `${detail.value.BelongingStreet || ''}${detail.value.ResidentialAddress || ''}`,
        success: () => {
          navigationLoading.value = false
        },
        fail: (error) => {
          console.error('导航失败:', error)
          toast.error('导航失败，请稍后重试')
          navigationLoading.value = false
        }
      })
    } else {
      // 非微信小程序环境，使用uni-app的openLocation
      uni.openLocation({
        latitude: lat,
        longitude: lng,
        scale: 16,
        name: detail.value.PumpHouseName || '泵房位置',
        address: `${detail.value.BelongingStreet || ''}${detail.value.ResidentialAddress || ''}`,
        success: () => {
          navigationLoading.value = false
        },
        fail: (error) => {
          console.error('导航失败:', error)
          toast.error('导航失败，请稍后重试')
          navigationLoading.value = false
        }
      })
    }
  } catch (error) {
    uni.hideLoading()
    navigationLoading.value = false
    console.error('openNavigation 执行失败:', error)

    // 根据错误类型给出不同的提示
    if (error.message && error.message.includes('网络')) {
      toast.error('网络连接失败，请检查网络后重试')
    } else if (error.message && error.message.includes('坐标转换')) {
      toast.error('坐标转换失败，请稍后重试')
    } else {
      toast.error('导航功能暂时不可用，请稍后重试')
    }
  }
}

// 分享功能
onShareAppMessage(() => {
  uni.vibrateShort({ type: 'medium' })
  return {
    title: `泵房详情 - ${detail.value?.PumpHouseName || '泵房信息'}`,
    path: `/pages/pump-house/detail?pumpRoomNumber=${detail.value?.PumpRoomNumber || ''}`
  }
})
</script>

<style lang="less" scoped>
// 主要样式区域

// 优化的泵房详情样式 - 使用更现代的配色方案
.pump-house-header {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  margin: 20rpx 0 30rpx 0;
  padding: 30rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(79, 70, 229, 0.15);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20rpx;
}

.pump-house-info {
  flex: 1;
}

.pump-house-title {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.status-info {
  display: flex;
  gap: 12rpx;
  align-items: center;
  flex-wrap: wrap;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.batch-info {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.9);
  color: #4f46e5;
}

// 操作按钮区域
.action-buttons {
  display: flex;
  gap: 24rpx;
  align-items: flex-start;
}

// 导航按钮
.navigation-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.3), 0 2rpx 8rpx rgba(69, 160, 73, 0.2);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2rpx) scale(1.02);
    box-shadow: 0 12rpx 40rpx rgba(76, 175, 80, 0.4), 0 4rpx 12rpx rgba(69, 160, 73, 0.3);
    background: linear-gradient(135deg, #5cbf60 0%, #4caf50 100%);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.3), 0 1rpx 6rpx rgba(69, 160, 73, 0.2);
  }

  // 添加按压动画效果
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
  }

  &:active::before {
    width: 200rpx;
    height: 200rpx;
  }
}

.nav-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;

  .navigation-button:hover & {
    transform: scale(1.1) rotate(5deg);
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 6rpx 20rpx rgba(255, 255, 255, 0.2);
  }

  .navigation-button:active & {
    transform: scale(1.05) rotate(2deg);
  }
}

.nav-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #fff;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;

  .navigation-button:hover & {
    transform: translateY(-1rpx);
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }

  .navigation-button:active & {
    transform: translateY(0);
  }
}

// 加载动画
.loading-spinner {
  width: 18rpx;
  height: 18rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 分享按钮
.share-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12rpx 16rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1), 0 2rpx 8rpx rgba(0, 0, 0, 0.05), inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 80rpx;

  &:hover {
    transform: translateY(-2rpx) scale(1.02);
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15), 0 4rpx 12rpx rgba(0, 0, 0, 0.08), inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1), 0 1rpx 6rpx rgba(0, 0, 0, 0.05), inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  }
}

.share-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4), 0 2rpx 8rpx rgba(118, 75, 162, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .share-button:hover & {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.5), 0 3rpx 12rpx rgba(118, 75, 162, 0.4);
  }

  .share-button:active & {
    transform: scale(1.05) rotate(2deg);
  }
}

.share-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #4f46e5;
  line-height: 1.2;
  transition: all 0.3s ease;
}

// 状态样式 - 更现代的配色
.status-completed {
  background: linear-gradient(135deg, #10b981, #059669) !important;
}

.status-progress {
  background: linear-gradient(135deg, #f59e0b, #d97706) !important;
}

.status-pending {
  background: linear-gradient(135deg, #6b7280, #4b5563) !important;
}

.status-error {
  background: linear-gradient(135deg, #ef4444, #dc2626) !important;
}

// 信息区块样式 - 现代卡片设计
.info-section {
  margin-bottom: 32rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 50%, rgba(241, 245, 249, 0.85) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(79, 70, 229, 0.12), 0 2rpx 8rpx rgba(0, 0, 0, 0.06), inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  overflow: hidden;
  border: 1rpx solid rgba(79, 70, 229, 0.12);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.info-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.02) 0%, rgba(124, 58, 237, 0.01) 50%, rgba(59, 130, 246, 0.02) 100%);
  pointer-events: none;
  z-index: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 32rpx 28rpx;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.08) 0%, rgba(124, 58, 237, 0.06) 50%, rgba(59, 130, 246, 0.08) 100%);
  font-size: 30rpx;
  font-weight: 800;
  color: #1e293b;
  border-bottom: 2rpx solid rgba(79, 70, 229, 0.15);
  position: relative;
  z-index: 1;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(180deg, #4f46e5 0%, #7c3aed 50%, #3b82f6 100%);
  border-radius: 0 3rpx 3rpx 0;
  box-shadow: 2rpx 0 8rpx rgba(79, 70, 229, 0.3);
}

.section-title::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 28rpx;
  transform: translateY(-50%);
  width: 40rpx;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.3), transparent);
}

.info-content {
  padding: 32rpx 28rpx 0;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
  position: relative;
  z-index: 1;
}

// 信息行布局 - 现代卡片内容设计
.info-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 24rpx;
}

.info-row.full {
  flex-direction: column;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding: 28rpx 24rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 50%, rgba(241, 245, 249, 0.85) 100%);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(79, 70, 229, 0.1);
  box-shadow: 0 4rpx 16rpx rgba(79, 70, 229, 0.08), 0 1rpx 4rpx rgba(0, 0, 0, 0.04), inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.info-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 5rpx;
  background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 2rpx 0 6rpx rgba(59, 130, 246, 0.3);
}

.info-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.01) 0%, rgba(124, 58, 237, 0.005) 50%, rgba(59, 130, 246, 0.01) 100%);
  pointer-events: none;
  transition: opacity 0.4s ease;
  opacity: 0;
}

.info-item:hover {
  transform: translateY(-3rpx) scale(1.02);
  box-shadow: 0 8rpx 24rpx rgba(79, 70, 229, 0.15), 0 2rpx 8rpx rgba(0, 0, 0, 0.08), inset 0 1rpx 0 rgba(255, 255, 255, 0.95);
  border-color: rgba(79, 70, 229, 0.2);
}

.info-item:hover::before {
  width: 8rpx;
  background: linear-gradient(180deg, #4f46e5 0%, #7c3aed 50%, #6366f1 100%);
  box-shadow: 3rpx 0 10rpx rgba(79, 70, 229, 0.4);
}

.info-item:hover::after {
  opacity: 1;
}

.info-item.important {
  background: linear-gradient(145deg, rgba(254, 242, 242, 0.95) 0%, rgba(254, 226, 226, 0.9) 50%, rgba(252, 165, 165, 0.1) 100%);
  border-color: rgba(239, 68, 68, 0.2);
  box-shadow: 0 4rpx 16rpx rgba(239, 68, 68, 0.12), 0 1rpx 4rpx rgba(0, 0, 0, 0.04), inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.info-item.important::before {
  background: linear-gradient(180deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
  box-shadow: 2rpx 0 6rpx rgba(239, 68, 68, 0.4);
}

.info-item.important:hover::before {
  background: linear-gradient(180deg, #f87171 0%, #ef4444 50%, #dc2626 100%);
  box-shadow: 3rpx 0 10rpx rgba(239, 68, 68, 0.5);
}

.label {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6rpx;
  text-transform: uppercase;
  letter-spacing: 0.5rpx;
  margin-bottom: 2rpx;
}

.value {
  font-size: 28rpx;
  color: #1e293b;
  font-weight: 700;
  word-break: break-all;
  line-height: 1.4;
}

.value.highlight {
  color: #4f46e5;
  font-weight: 800;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.value.warning {
  color: #ef4444;
  font-weight: 700;
}

.value.success {
  color: #10b981;
  font-weight: 700;
}

.value.remark {
  font-size: 26rpx;
  line-height: 1.6;
  color: #475569;
  font-weight: 500;
}

.phone-number {
  color: #4f46e5;
  font-weight: 700;
  text-decoration: none;
}

// 空值样式
.empty-value {
  color: #94a3b8 !important;
  font-style: italic;
  opacity: 0.8;
}

// 加压区状态特殊样式
.pressure-zone-status {
  background: linear-gradient(145deg, rgba(139, 92, 246, 0.08) 0%, rgba(168, 85, 247, 0.06) 50%, rgba(147, 51, 234, 0.08) 100%) !important;
  border-color: rgba(139, 92, 246, 0.2) !important;
  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.12), 0 1rpx 4rpx rgba(0, 0, 0, 0.04), inset 0 1rpx 0 rgba(255, 255, 255, 0.9) !important;
}

.pressure-zone-status::before {
  background: linear-gradient(180deg, #8b5cf6 0%, #a855f7 50%, #9333ea 100%) !important;
  box-shadow: 2rpx 0 6rpx rgba(139, 92, 246, 0.4) !important;
}

.pressure-zone-status:hover::before {
  background: linear-gradient(180deg, #a78bfa 0%, #8b5cf6 50%, #a855f7 100%) !important;
  box-shadow: 3rpx 0 10rpx rgba(139, 92, 246, 0.5) !important;
}

.status-text {
  font-size: 26rpx !important;
  line-height: 1.6 !important;
  color: #374151 !important;
  font-weight: 600 !important;
  word-break: break-word !important;
  white-space: pre-wrap !important;
  text-align: left !important;
}

// 坐标显示样式 - 特殊卡片设计
.coordinate-display {
  background: linear-gradient(145deg, rgba(240, 249, 255, 0.95) 0%, rgba(224, 242, 254, 0.9) 50%, rgba(186, 230, 253, 0.85) 100%);
  backdrop-filter: blur(15rpx);
  border-radius: 20rpx;
  padding: 32rpx 28rpx;
  margin-bottom: 28rpx;
  border: 1rpx solid rgba(14, 165, 233, 0.25);
  box-shadow: 0 6rpx 24rpx rgba(14, 165, 233, 0.15), 0 2rpx 8rpx rgba(0, 0, 0, 0.05), inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.coordinate-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
  box-shadow: 0 2rpx 8rpx rgba(14, 165, 233, 0.4);
}

.coordinate-display::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.02) 0%, rgba(2, 132, 199, 0.01) 50%, rgba(3, 105, 161, 0.02) 100%);
  pointer-events: none;
}

.coordinate-display:hover {
  transform: translateY(-2rpx) scale(1.01);
  box-shadow: 0 10rpx 32rpx rgba(14, 165, 233, 0.2), 0 4rpx 12rpx rgba(0, 0, 0, 0.08), inset 0 1rpx 0 rgba(255, 255, 255, 0.95);
  border-color: rgba(14, 165, 233, 0.35);
}

.coordinate-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}

.coordinate-label {
  font-size: 26rpx;
  color: #475569;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.coordinate-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24rpx;
  position: relative;
  z-index: 1;
}

.coordinate-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  flex: 1;
}

.coordinate-type {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5rpx;
  opacity: 0.8;
}

.coordinate-value {
  font-size: 24rpx;
  font-weight: 900;
  color: #0ea5e9;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-shadow: 0 2rpx 4rpx rgba(14, 165, 233, 0.3);
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background-color: rgba(14, 165, 233, 0.1);
  border: 1rpx solid rgba(14, 165, 233, 0.2);
  transition: all 0.3s ease;
}

.coordinate-separator {
  font-size: 28rpx;
  color: rgba(14, 165, 233, 0.4);
  font-weight: 300;
  opacity: 0.6;
}

.coordinate-empty {
  text-align: center;
  position: relative;
  z-index: 1;
}

// 表格样式
.table-section {
  margin-top: 32rpx;
  padding: 24rpx 0;
  border-top: 1rpx solid rgba(139, 92, 246, 0.15);
  position: relative;
}

.table-section::before {
  content: '';
  position: absolute;
  top: -1rpx;
  left: 0;
  width: 60rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #8b5cf6 0%, #a855f7 100%);
  border-radius: 1rpx;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  font-weight: 700;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5rpx;
}

.data-table {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(79, 70, 229, 0.08), 0 1rpx 4rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(79, 70, 229, 0.1);
}

.table-header {
  display: flex;
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
  font-weight: 700;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid rgba(79, 70, 229, 0.1);
  transition: all 0.3s ease;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(168, 85, 247, 0.03) 100%);
}

.table-cell {
  flex: 1;
  padding: 20rpx 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 26rpx;
  border-right: 1rpx solid rgba(79, 70, 229, 0.1);
}

.zone-cell {
  flex: 0.6 !important;
  min-width: 80rpx;
}

.table-cell:last-child {
  border-right: none;
}

.header-cell {
  font-weight: 800;
  font-size: 24rpx;
  text-transform: uppercase;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.header-cell:first-child {
  flex: 0.6 !important;
  min-width: 80rpx;
}

.zone-cell {
  flex: 0.6 !important;
  min-width: 80rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  position: relative;
}

.low-zone {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.mid-zone {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.high-zone {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.data-cell {
  font-weight: 600;
  color: #1e293b;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
}

.pressure-cell {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  color: #8b5cf6;
  font-weight: 800;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(168, 85, 247, 0.05) 100%);
}

// 图片展示样式 - 现代画廊设计
.image-section {
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 2rpx solid rgba(79, 70, 229, 0.15);
  position: relative;
}

.image-section::before {
  content: '';
  position: absolute;
  top: -1rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 3rpx;
  background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #3b82f6 100%);
  border-radius: 2rpx;
  box-shadow: 0 2rpx 8rpx rgba(79, 70, 229, 0.3);
}

.image-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
  font-size: 30rpx;
  font-weight: 800;
  color: #1e293b;
  position: relative;
}

.image-title::after {
  content: '';
  flex: 1;
  height: 2rpx;
  background: linear-gradient(90deg, rgba(79, 70, 229, 0.3), transparent);
  margin-left: 16rpx;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220rpx, 1fr));
  gap: 24rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 20rpx;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6rpx 24rpx rgba(79, 70, 229, 0.12), 0 2rpx 8rpx rgba(0, 0, 0, 0.06), inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  border: 2rpx solid rgba(255, 255, 255, 0.9);
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
}

.image-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(124, 58, 237, 0.03) 50%, rgba(59, 130, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  z-index: 1;
}

.image-item:hover {
  transform: translateY(-6rpx) scale(1.03);
  box-shadow: 0 16rpx 48rpx rgba(79, 70, 229, 0.2), 0 6rpx 16rpx rgba(0, 0, 0, 0.12), inset 0 1rpx 0 rgba(255, 255, 255, 0.95);
  border-color: rgba(79, 70, 229, 0.4);
}

.image-item:hover::before {
  opacity: 1;
}

.image-item:active {
  transform: translateY(-3rpx) scale(0.98);
  transition: all 0.2s ease;
}

.pump-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 0;
}

.image-item:hover .pump-image {
  transform: scale(1.08);
  filter: brightness(1.05) contrast(1.05);
}

// 添加页面整体背景
.detail {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 50%, rgba(226, 232, 240, 0.8) 100%);
  min-height: 100vh;
  position: relative;
}

.detail::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%, rgba(79, 70, 229, 0.03) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(124, 58, 237, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}
</style>
